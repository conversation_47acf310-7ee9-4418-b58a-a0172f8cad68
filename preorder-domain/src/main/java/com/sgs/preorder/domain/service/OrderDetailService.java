package com.sgs.preorder.domain.service;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import com.aizuda.snailjob.client.core.annotation.Retryable;
import com.aizuda.snailjob.client.core.retryer.RetryType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.constant.SGSConstant;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.log.SystemLogHelper;
import com.sgs.framework.log.enums.SystemLogType;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.model.enums.*;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.mybatis.config.DatabaseContextHolder;
import com.sgs.framework.mybatis.enums.DatabaseTypeEnum;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.framework.tool.utils.StringPool;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQualificationTypeUpdateReq;
import com.sgs.gpo.facade.model.sci.req.GpoSciOrderToTrfReq;
import com.sgs.gpo.facade.model.sci.req.GpoSciTrfSyncReq;
import com.sgs.gpo.facade.model.sci.rsp.Order2TrfRsp;
import com.sgs.gpo.facade.sci.SciFacade;
import com.sgs.gpo.facade.temp.OrderTempFacade;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.info.BizLogInfo;
import com.sgs.otsnotes.facade.*;
import com.sgs.otsnotes.facade.model.common.BuReportWorkflowDTO;
import com.sgs.otsnotes.facade.model.dto.DueDateChangeDTO;
import com.sgs.otsnotes.facade.model.dto.GpnOrderReportDTO;
import com.sgs.otsnotes.facade.model.dto.ReportCountryOfDestinationDTO;
import com.sgs.otsnotes.facade.model.dto.subcontract.SubContractExternalRelationshipDTO;
import com.sgs.otsnotes.facade.model.enums.DeliverToType;
import com.sgs.otsnotes.facade.model.enums.EmailTemplateTypeEnums;
import com.sgs.otsnotes.facade.model.enums.ReportRequirementEnum;
import com.sgs.otsnotes.facade.model.enums.SubContractStatusEnum;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractInfo;
import com.sgs.otsnotes.facade.model.req.*;
import com.sgs.otsnotes.facade.model.req.copy.SyncCustomerInfo;
import com.sgs.otsnotes.facade.model.req.copy.SyncSampleInfo;
import com.sgs.otsnotes.facade.model.req.copy.SyncTestLineInfo;
import com.sgs.otsnotes.facade.model.req.gpn.CheckTLStandFullNameReq;
import com.sgs.otsnotes.facade.model.req.gpn.ToTestReq;
import com.sgs.otsnotes.facade.model.req.order.SyncOrderInfo;
import com.sgs.otsnotes.facade.model.req.processrecord.ProcessRecordForReportDTO;
import com.sgs.otsnotes.facade.model.req.report.OrderNosReq;
import com.sgs.otsnotes.facade.model.req.report.ReportTemplateSettingReq;
import com.sgs.otsnotes.facade.model.req.report.UpdateReportCountryOfDestinationReq;
import com.sgs.otsnotes.facade.model.req.subcontract.QuerySubContractListReq;
import com.sgs.otsnotes.facade.model.req.testLine.QueryTestLineLabSectionReq;
import com.sgs.otsnotes.facade.model.req.testLine.UpdateLabSectionReq;
import com.sgs.otsnotes.facade.model.rsp.OrderSubContractRsp;
import com.sgs.otsnotes.facade.model.rsp.TestLineLabSectionRsp;
import com.sgs.otsnotes.facade.model.rsp.TestSampleRsp;
import com.sgs.otsnotes.facade.model.rsp.gpn.JobListRsp;
import com.sgs.preorder.config.buparam.CancelOrderConfig;
import com.sgs.preorder.config.buparam.DiscountPersonConfig;
import com.sgs.preorder.core.common.EventType;
import com.sgs.preorder.core.common.PreEvent;
import com.sgs.preorder.core.common.StandardObjectType;
import com.sgs.preorder.core.common.UserHelper;
import com.sgs.preorder.core.config.InterfaceConfig;
import com.sgs.preorder.core.enums.BizLogConstant;
import com.sgs.preorder.core.exception.RetryException;
import com.sgs.preorder.core.order.dto.ExternalOrderDto;
import com.sgs.preorder.core.order.dto.OrderMatrixDto;
import com.sgs.preorder.core.order.dto.OrderReportDto;
import com.sgs.preorder.core.order.dto.OrderReportMatrixDto;
import com.sgs.preorder.core.thread.ThreadPoolContextTaskExecutor;
import com.sgs.preorder.core.util.*;
import com.sgs.preorder.dbstorages.mybatis.extmapper.order.*;
import com.sgs.preorder.dbstorages.mybatis.extmapper.tag.TagValueExtMapper;
import com.sgs.preorder.dbstorages.mybatis.extmapper.todolist.GPOToDoListExtMapper;
import com.sgs.preorder.dbstorages.mybatis.mapper.*;
import com.sgs.preorder.dbstorages.mybatis.model.*;
import com.sgs.preorder.domain.convertor.ReferenceConvertor;
import com.sgs.preorder.domain.dto.CloseOrderRequest;
import com.sgs.preorder.domain.dto.buparams.QuotationSendBuParamsDto;
import com.sgs.preorder.domain.service.busetting.IBuSettingService;
import com.sgs.preorder.domain.service.enquiry.IEnquiryService;
import com.sgs.preorder.domain.service.eventcenter.EventCenterService;
import com.sgs.preorder.domain.service.external.ExternalNoUtil;
import com.sgs.preorder.domain.service.gpoorder.IDffRowToColumnService;
import com.sgs.preorder.domain.service.lims.settings.buparam.IBuParamService;
import com.sgs.preorder.domain.service.operationhistory.IOperationHistoryService;
import com.sgs.preorder.domain.service.productSample.IProductSampleService;
import com.sgs.preorder.domain.service.tag.ITagService;
import com.sgs.preorder.domain.service.tracking.ITrackingService;
import com.sgs.preorder.domain.service.trf.CallTrfInterface;
import com.sgs.preorder.domain.service.trf.SciTrfService;
import com.sgs.preorder.domain.service.validator.TestInfoValidator;
import com.sgs.preorder.domain.service.validator.TrfInfoValidator;
import com.sgs.preorder.domain.util.HashUtils;
import com.sgs.preorder.domain.util.ToTestHashHelper;
import com.sgs.preorder.facade.model.annotation.ObjectSetting;
import com.sgs.preorder.facade.model.dto.EmailAttachmentDTO;
import com.sgs.preorder.facade.model.dto.EmailAutoSendDTO;
import com.sgs.preorder.facade.model.dto.OrderQuotationDTO;
import com.sgs.preorder.facade.model.dto.busetting.BuObjectSettingDTO;
import com.sgs.preorder.facade.model.dto.busetting.BuObjectTemplateAllDTO;
import com.sgs.preorder.facade.model.dto.busetting.BuObjectTemplateDTO;
import com.sgs.preorder.facade.model.dto.customer.CustomerExtDTO;
import com.sgs.preorder.facade.model.dto.customer.CustomerInstanceDTO;
import com.sgs.preorder.facade.model.dto.dff.DFFFormRspDTO;
import com.sgs.preorder.facade.model.dto.dff.DffFormAttrDTO;
import com.sgs.preorder.facade.model.dto.enquiry.EnquiryInfoDTO;
import com.sgs.preorder.facade.model.dto.order.*;
import com.sgs.preorder.facade.model.dto.orderext.OrderExtDTO;
import com.sgs.preorder.facade.model.dto.orderext.OrderExtTrfTemplateDTO;
import com.sgs.preorder.facade.model.dto.productextfields.SampleExtFieldDto;
import com.sgs.preorder.facade.model.dto.tag.TagSearchDTO;
import com.sgs.preorder.facade.model.dto.tag.TagValueDTO;
import com.sgs.preorder.facade.model.dto.tag.TagValueSelectDTO;
import com.sgs.preorder.facade.model.email.EmailFile;
import com.sgs.preorder.facade.model.email.OrderCancelEmailParam;
import com.sgs.preorder.facade.model.enums.CustomerType;
import com.sgs.preorder.facade.model.enums.OperationType;
import com.sgs.preorder.facade.model.enums.OrderBizType;
import com.sgs.preorder.facade.model.enums.OrderPersonType;
import com.sgs.preorder.facade.model.enums.OrderStatusEnum;

import com.sgs.preorder.facade.model.enums.ReportStatus;
import com.sgs.preorder.facade.model.enums.*;
import com.sgs.preorder.facade.model.info.LabInfo;
import com.sgs.preorder.facade.model.info.*;
import com.sgs.preorder.facade.model.info.customer.CustomerBuyerGroupInfo;
import com.sgs.preorder.facade.model.info.externalOrder.GpoExternalOrderInfo;
import com.sgs.preorder.facade.model.info.user.UserLabBuInfo;
import com.sgs.preorder.facade.model.po.LabInfoPO;
import com.sgs.preorder.facade.model.po.SlOrderInfoPO;
import com.sgs.preorder.facade.model.req.*;
import com.sgs.preorder.facade.model.req.customer.CustomerDetailReq;
import com.sgs.preorder.facade.model.req.customer.CustomerWeightReq;
import com.sgs.preorder.facade.model.req.customer.WeightByBuyerAndAgentReq;
import com.sgs.preorder.facade.model.req.externalOrder.GpoExternalOrderNoReq;
import com.sgs.preorder.facade.model.req.externalOrder.GpoExternalReportNoForReportNoReq;
import com.sgs.preorder.facade.model.req.order.OrderCancelFileReq;
import com.sgs.preorder.facade.model.req.order.OrderCancelReq;
import com.sgs.preorder.facade.model.req.order.OrderDetailConfigReq;
import com.sgs.preorder.facade.model.rsp.*;
import com.sgs.preorder.facade.model.rsp.customer.CustomerExtNewListRsp;
import com.sgs.preorder.facade.model.rsp.order.OrderDetailConfigRsp;
import com.sgs.preorder.integration.client.*;
import com.sgs.preorder.integration.client.setting.buparam.IBUParamService;
import com.sgs.priceengine.facade.QuotationFacade;
import com.sgs.priceengine.facade.QuotationMatrixFacade;
import com.sgs.priceengine.facade.model.enums.CustomerUsage;
import com.sgs.priceengine.facade.model.enums.QuotationStatus;
import com.sgs.priceengine.facade.model.enums.ToBossType;
import com.sgs.priceengine.facade.model.request.*;
import com.sgs.priceengine.facade.model.response.QuotationHeadRsp;
import com.sgs.usermanagement.req.QuerySalesDiscountReq;
import com.sgs.usermanagement.rsp.QuerySalesDiscountRsp;
import com.sgs.workflow.facade.WorkflowFacade;
import com.sgs.workflow.model.process.info.ProcessInstanceInfo;
import com.sgs.workflow.model.process.req.ProcessInstanceQueryReq;
import com.sgs.workflow.model.process.req.StartProcessReq;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OrderDetailService {
    private static final Logger logger = LoggerFactory.getLogger(OrderDetailService.class);
    @Autowired
    private OrderDetailMapper orderDetailMapper;
    @Autowired
    private ProductMapper productMapper;
    @Autowired
    private TestRequestMapper testRequestMapper;
    @Autowired
    private TokenClient tokenClient;
    @Autowired
    private UserClient userClient;
    @Autowired
    private KafkaService kafkaService;
    @Autowired
    private ExpectDueDateService expectDueDateService;
    @Autowired
    private OrderDetailImpl orderDetailImpl;
    @Autowired
    private CallTrfInterface callTrfInterface;
    @Autowired
    private FileClient fileClient;
    @Autowired
    private DffClient dffClient;
    @Autowired
    private OrderClient orderClient;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private TestRequestContactsInfoMapper testRequestContactsInfoMapper;
    @Autowired
    private NotesClient notesClient;
    @Autowired
    private CareLabelMapper careLabelMapper;
    @Autowired
    private IDffRowToColumnService dffRowToColumnService;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private CrossLabService crossLabService;
    @Autowired
    private CustomerInfoMapper customerInfoMapper;
    @Autowired
    private TestRequestInfoMapper testRequestInfoMapper;
    @Autowired
    private OrderFacade orderFacade;
    @Autowired
    private SubContractClient subContractClient;
    @Autowired
    private ObjectTagInfoMapper objectTagInfoMapper;
    @Autowired
    private TagValueMapper tagValueMapper;
    @Autowired
    private TagMapper tagMapper;
    @Autowired
    private TagValueExtMapper tagValueExtMapper;
    @Autowired
    private ParcelMapper parcelMapper;
    @Autowired
    private IBuSettingService buSettingService;
    @Autowired
    private LabMapper labMapper;
    @Autowired
    private QuotationMatrixFacade quotationMatrixFacade;
    @Autowired
    private OrderInfoMapper orderInfoMapper;
    @Autowired
    private BizLogClient bizLogClient;
    @Autowired
    private QuotationFacade quotationFacade;
    @Autowired
    private FrameWorkClient frameWorkClient;
    @Autowired
    private CustomerClient customerClient;

    @Autowired
    private IEnquiryService iEnquiryService;

    @Autowired
    private TestMatrixFacade testMatrixFacade;
    @Autowired
    private ReportFacade reportFacade;

    @Autowired
    private CustomerService customerService;
    @Autowired
    private IOperationHistoryService iOperationHistoryService;
    @Autowired
    private SlOrderMapper slOrderMapper;
    @Autowired
    private OrderAttachmentInfoMapper orderAttachmentInfoMapper;
    @Autowired
    private OrderService orderService;
    @Autowired
    private ExternalNoService externalNoService;
    @Autowired
    private ITrackingService trackingService;
    @Autowired
    private ITagService tagService;
    @Autowired
    private ReportService reportService;
    @Autowired
    private OrderTrfRelationshipMapper orderTrfRelationshipMapper;
    @Autowired
    private SubContractFacade subContractFacade;
    @Autowired
    private InterfaceConfig interfaceConfig;
    @Autowired
    private LabInfoMapper labInfoMapper;
    @Autowired
    private NotificationClient notificationClient;
    @Autowired
    private OrderPaidUpInfoMapper orderPaidUpInfoMapper;
    @Autowired
    private WorkflowFacade workflowFacade;
    @Autowired
    private SystemLogHelper systemLogHelper;
    @Autowired
    private ThreadPoolContextTaskExecutor taskExecutor;
    @Autowired
    private TrackingClient trackingClient;
    @Autowired
    private ExternalNoUtil externalNoUtil;
    @Resource
    private TestInfoValidator testInfoValidator;
    @Resource
    private TrfInfoValidator trfInfoValidator;
    @Resource
    private SciTrfService sciTrfService;

    @Resource
    private EventCenterService eventCenterService;

    @Resource
    private LocalIlayerClient ilayerClient;

    @Resource
    private OrderExtService orderExtService;


    @Resource
    private StandardOrderService standardOrderService;
    @Autowired
    private IBuParamService buParamService;
    @Autowired
    private GpnJobFacade gpnJobFacade;
    @Autowired
    private TestLineFacade testLineFacade;
    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;
    @Autowired
    private SciFacade sciFacade;
    @Autowired
    private ScheduleTaskClient scheduleTaskClient;
    @Autowired
    private IProductSampleService productSampleService;
    @Autowired
    private IBUParamService ibuParamService;
    @Autowired
    private GPOToDoListExtMapper gpoToDoListExtMapper;
    @Autowired
    private OrderTempFacade orderTempFacade;
    @Autowired
    private SlOrderInfoMapper slOrderInfoMapper;
    @Autowired
    private OrderOperationHistoryMapper orderOperationHistoryMapper;


    public CustomResult getOrderDetailInfo(String orderId) {
        return getOrderDetailInfo(orderId, tokenClient.getToken());
    }

    public CustomResult getOrderDetailInfo(String orderId, String token) {
        if (Func.isEmpty(token)) {
            token = tokenClient.getToken();
        }
        return getOrderDetailInfo(orderId, token, true);
    }

    /**
     * getOrderDetailInfo
     *
     * @param orderId
     * @return
     */
    public CustomResult getOrderDetailInfo(String orderId, String token, Boolean needTag) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        CustomResult rspResult = new CustomResult();

        UserInfo user = SecurityContextHolder.getUserInfoFillSystem();

        OrderDetailDto orderDetail = new OrderDetailDto();
        List<BuParamValueRsp> buSettings = new ArrayList<>();
        orderDetail.setBuSettings(buSettings);
        OrderHeaderInfo header = new OrderHeaderInfo();

        if (Func.isNotEmpty(user)) {
            Map<String, List<String>> dimensionMap = user.getDimensionMap();
            if (dimensionMap != null) {
                List<String> block_tops = dimensionMap.get("block_tops");
                if (block_tops != null && block_tops.size() > 0) {
                    header.setBlockTops(block_tops.get(0));
                }
            }
        }
        UserLabBuInfo userLabBu = null;
        if (Func.isNotEmpty(token)) {
            userLabBu = userClient.getUserLabBuInfo(token);
            orderDetail.setLab(userLabBu);
        }
        if (Func.isNotEmpty(userLabBu)) {
            // 获取DiscountPerson的取值规则，如果是OrderOwner，查看当前用户是否属于折扣审批人，属于默认，否则不处理
            DiscountPersonConfig discountPersonConfig = buSettingService.getDiscountPersonConfig(userLabBu.getProductLineCode(), userLabBu.getLocationCode());
            if (Func.isNotEmpty(discountPersonConfig)) {
                String model = discountPersonConfig.getModel();
                buSettings.add(discountPersonConfig.getBuParamValue());
                if (Func.equals(Constants.BU_PARAM.QUOTATION.DISCOUNT_PERSION.MODEL.ORDER_OWNER, model)) {
                    QuerySalesDiscountReq querySalesDiscountReq = new QuerySalesDiscountReq();
                    querySalesDiscountReq.setBuCode(userLabBu.getProductLineCode());
                    querySalesDiscountReq.setLocationCode(userLabBu.getLocationCode());
                    querySalesDiscountReq.setProductCategory(header.getProductCategory());
                    querySalesDiscountReq.setRegionAccount(user.getRegionAccount());
                    QuerySalesDiscountRsp querySalesDiscountRsp = userClient.getSalesDiscount(querySalesDiscountReq);
                    if (Func.isNotEmpty(querySalesDiscountRsp)) {
                        header.setSalesPerson(user.getRegionAccount());
                        //查询邮箱
                        String salesEmail = frameWorkClient.getEmailByRegionAccount(user.getRegionAccount(), userLabBu.getLocationCode());
                        OrderPersonInfo orderPersonInfo = new OrderPersonInfo();
                        orderPersonInfo.setPersonType(OrderPersonType.SALES.getCode());
                        orderPersonInfo.setRegionAccount(user.getRegionAccount());
                        orderPersonInfo.setEmail(salesEmail);
                        orderDetail.setSalesPerson(orderPersonInfo);
                    }
                }
            }


            // 获取SubcontractMode配置
            BuParamValueRsp objSubcontractModeConfig = buSettingService.getSubcontractMode(userLabBu.getProductLineCode(), userLabBu.getLocationCode());
            if (objSubcontractModeConfig!=null) {
                buSettings.add(objSubcontractModeConfig);
            }
        }

        if (userLabBu != null) {
            header.setOrganizationId(userLabBu.getOrganizationID());
            header.setOrganizationName(userLabBu.getOrganizationName());
            header.setLegalEntityCode(userLabBu.getLegalEntityCode());
            header.setLegalEntityName(userLabBu.getLegalEntityName());
            header.setLabName(userLabBu.getLabName());
            header.setPostfixItems(userLabBu.getPostfix());
            String orderNoMode = ibuParamService.getOrderNoMode(userLabBu.getProductLineCode(),userLabBu.getLabCode()).getData();
            if(com.sgs.preorder.core.constants.Constants.BU_PARAM.ObjectNo.OrderNo.VALUE.BLYMSPC.equals(orderNoMode) && Func.isEmpty(header.getPostfixItems())) {
                header.setPostfixItems(new String[]{"TX"});
            }
        }
        if (StringUtils.isBlank(orderId)) {
            //logger.info("getOrderDetailInfo userLabBu=" + JSON.toJSONString(userLabBu));
            header.setNewOrderId(UUID.randomUUID().toString());
            header.setcSName(user.getRegionAccount());
            header.setcSEmail(user.getEmail());
            header.setcSContact(user.getTelephone());
            header.setLabId(userLabBu.getLabId());
            header.setLabCode(userLabBu.getLabCode());

            /**
             * sameAsApplicant设置初始值
             */
            header.setSameAsApplicantFlag(SameAsApplicantEnum.FALSE.getStatus());
            header.setLocationId(userLabBu.getLocationId());
            header.setLocationCode(userLabBu.getLocationCode());

            header.setBuId(userLabBu.getProductLineId());
            header.setBuCode(userLabBu.getProductLineCode());

            header.setLabId(userLabBu.getLabId());
            header.setLabCode(userLabBu.getLabCode());

            header.setBindStatus(true);

            // TODO 正常是需要TeamCode的，现在我们
            // header.setResponsibleTeamCode(userLabBu.getTeamCode());
            header.setResponsibleTeamCode(userLabBu.getTeamName());

            //获取sampleNoRule
            BuParamValueRsp objBuParamValueRsp = this.getSamleNoRule(userLabBu.getProductLineCode());
            if (objBuParamValueRsp != null) {
                orderDetail.setSampleNoRule(objBuParamValueRsp.getParamValue());
            } else {
                rspResult.setMsg("Get Bu Param is failed for SampleNoRule .");
                return rspResult;
            }
            orderDetail.setHeaders(header);
            //读取DffCustomizeColumns
            List<DffCustomizeColumn> dffCustomizeColumnsList = this.getDffCustomizeColumns(userLabBu.getProductLineCode());
            orderDetail.setDffCustomizeColumnList(dffCustomizeColumnsList);
            rspResult.setData(orderDetail);
            rspResult.setSuccess(true);
            return rspResult;
        }
        OrderDetailInfo order = orderDetailMapper.getOrderDetailInfo(orderId);
        // 查询订单下是否存在状态为draft状态的report，如果有，前端可以使用renew功能
        orderDetail.setRenew(Func.isNotEmpty(orderDetailMapper.checkReportStatus(orderId)));
        header.setPostfix(order.getPostfix());
        if (order == null) {
            rspResult.setMsg("未找到对应的orderId信息.");
            return rspResult;
        }

        orderDetail.setOperationMode(NumberUtil.toInt(order.getOperationMode()));

        LabInfoPO lab = order.getLab();
        if (lab != null) {
            header.setLabId(lab.getLabID());
            header.setLabCode(lab.getLabCode());
        }
        //读取DffCustomizeColumns
        List<DffCustomizeColumn> dffCustomizeColumnsList = this.getDffCustomizeColumns(order.getBuCode());
        orderDetail.setDffCustomizeColumnList(dffCustomizeColumnsList);

        // region 【tb_general_order】
        // TODO 映射关系
        BeanUtils.copyProperties(order, header);
        if (Func.isNotEmpty(order.getOldOrderNo())) {
            List<String> externalOldOrderNoList = externalNoUtil.getExternalOrderNo(Lists.newArrayList(order.getOldOrderNo()));
            if (Func.isNotEmpty(externalOldOrderNoList) && Func.isNotEmpty(externalOldOrderNoList.get(0))) {
                header.setExternalOldOrderNo(externalOldOrderNoList.get(0));
            } else {
                header.setExternalOldOrderNo(order.getOldOrderNo());
            }
        }
        header.setOperationMode(order.getOperationMode());
        header.setGroupId(order.getGroupID());
        header.setNewOrderId(order.getOrderId());
        // TODO
        header.setActualStartDate(order.getServiceStartDate());
        header.setOrderStatusText(OrderStatus.getMessage(order.getOrderStatus()));
        header.setSampleReceiveDate(order.getSampleReceiveDate());
        header.setOrderConfirmDate(order.getOrderConfirmDate());
        OperationType operationType = OperationType.findCode(order.getOperationType());
        if (operationType != null) {
            header.setOperationTypeDesc(operationType.getCode());
        }
        // POSL-3919
        if (StringUtils.isNotEmpty(order.getRefReportNo())) {
            //根据内部报告号查询外部报告号
            String refReportNo = order.getRefReportNo();
            GpoExternalReportNoForReportNoReq gpoReportNoForExternalReportNoReq = new GpoExternalReportNoForReportNoReq();
            gpoReportNoForExternalReportNoReq.setReportNoList(Lists.newArrayList(refReportNo));
            gpoReportNoForExternalReportNoReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            List<ExternalReportNoRsp> externalReportNoRspList = externalNoService.getExternalReportNoByReportNo(gpoReportNoForExternalReportNoReq).getData();
            if (Func.isNotEmpty(externalReportNoRspList) && Func.isNotEmpty(externalReportNoRspList.get(0).getExternalReportNo())) {
                header.setRefReportNo(externalReportNoRspList.get(0).getExternalReportNo());
            } else {
                header.setRefReportNo(order.getRefReportNo());
            }
        }
        // endregion

        // region 【tb_sl_order】
        // TRF References
        List<OrderTrfRelInfo> orderTrfRelInfos = orderTrfRelationshipMapper.getTrfRelationshipByOrderId(order.getOrderId());
        List<ReferenceInfo> trfReferences = new ArrayList<>();
        if (Func.isNotEmpty(orderTrfRelInfos)) {
            trfReferences = ReferenceConvertor.toReferenceInfoList(orderTrfRelInfos);
        }
        header.setReferences(trfReferences);
        SlOrderInfoPO slOrder = order.getSlOrder();
        if (slOrder != null) {
            header.setSlOrderId(slOrder.getSlOrderId());
//            ReportInfo report = order.getReport();

            // 作为SODA系统，只有当Order Status=New/Confirmed/Testing/Reporting
            // 且Order下所有的的Report Status=New/Combined/Draft/Approved时，才能解除绑定
            // 查询订单下报告列表，用来校验报告状态
            Boolean reportUnbindFlag = true;
            Boolean reportBindFlag = true;
            com.sgs.otsnotes.facade.model.req.report.OrderNosReq orderNosReq = new com.sgs.otsnotes.facade.model.req.report.OrderNosReq();
            orderNosReq.setOrderNoList(Lists.newArrayList(order.getOrderNo()));
            orderNosReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            BaseResponse<List<GpnOrderReportDTO>> reportByOrderNoList = reportFacade.getReportByOrderNos(orderNosReq);
            if (Func.isNotEmpty(reportByOrderNoList) && Func.isNotEmpty(reportByOrderNoList.getData())) {
                //允许解绑trf的报告状态 (排除无效的报告（Cancel、Reworked、Replaced、Pending），剩余的报告状态全部为New、Draft、approved)
                List<GpnOrderReportDTO> reportDTOList = reportByOrderNoList.getData();
                List<GpnOrderReportDTO> unbindReportDTOS = reportDTOList.stream().filter(i -> !ReportStatus.checkStatus(i.getReportStatus(), ReportStatus.Cancelled, ReportStatus.Reworked, ReportStatus.Replaced))
                        .filter(i -> !ReportStatus.checkStatus(i.getReportStatus(), ReportStatus.New, ReportStatus.Draft, ReportStatus.Approved)).collect(Collectors.toList());
                if (Func.isNotEmpty(unbindReportDTOS)) {
                    reportUnbindFlag = false;
                }
                //允许绑定trf的报告状态 (排除无效的报告（Cancel、Reworked、Replaced、Pending），剩余的报告状态全部为New)
                List<GpnOrderReportDTO> bindReportDTOS = reportDTOList.stream().filter(i -> !ReportStatus.checkStatus(i.getReportStatus(), ReportStatus.Cancelled, ReportStatus.Reworked, ReportStatus.Replaced))
                        .filter(i -> !ReportStatus.checkStatus(i.getReportStatus(), ReportStatus.New)).collect(Collectors.toList());
                if (Func.isNotEmpty(bindReportDTOS)) {
                    reportBindFlag = false;
                }
            }

            //解除绑定的报告条件
            List<ReferenceInfo> otsOrSubContractReferences = trfReferences.stream().filter(i -> RefSystemIdEnum.check(i.getRefSystemId(), RefSystemIdEnum.SubContract, RefSystemIdEnum.OTS)).collect(Collectors.toList());
            boolean order2Trf = trfReferences.parallelStream().anyMatch(ref -> TrfSourceType.Order2TRF.getSourceType().equalsIgnoreCase(ref.getTrfSourceType()));
            header.setTrfUnbindStatus(!order2Trf && isInterfaceRefSystemSource(header.getReferences())
                    && OrderStatus.checkStatus(order.getOrderStatus(), OrderStatus.New, OrderStatus.Confirmed, OrderStatus.Testing, OrderStatus.Reporting)
                    && reportUnbindFlag);
            // 需要校验Order Status=New/Confirmed/testing才能进行绑定
            // reportStatus = new
            // GPO-15238 暂时去掉 OrderStatus.Testing, OrderStatus.Reporting GPO2-16596 开放支持bind
            header.setTrfBindStatus(!order2Trf && !isInterfaceRefSystemSource(header.getReferences()) &&
                    OrderStatus.checkStatus(order.getOrderStatus(), OrderStatus.New, OrderStatus.Confirmed, OrderStatus.Testing, OrderStatus.Reporting)
                    && reportBindFlag);
            header.setUnbindStatus(Func.isNotEmpty(otsOrSubContractReferences)
                    && OrderStatus.checkStatus(order.getOrderStatus(), OrderStatus.New, OrderStatus.Confirmed, OrderStatus.Testing, OrderStatus.Reporting)
            );

            // 需要校验Order Status=New/Confirmed/testing才能进行绑定

            header.setBindStatus(Func.isEmpty(otsOrSubContractReferences) &&
                            OrderStatus.checkStatus(order.getOrderStatus(), OrderStatus.New, OrderStatus.Confirmed, OrderStatus.Reporting)
//                    && ReportStatus.checkStatus(report.getReportStatus(), ReportStatus.New)
            );
            // TODO BOSS Order Number
            /*header.setReferenceNo(slOrder.getReferenceNo());
            header.setReferenceId(slOrder.getReferenceId());
            List<OrderTrfRelInfo>  orderTrfRelInfos = orderTrfRelationshipMapper.getTrfRelationshipByOrderId(order.getOrderId());
            if(Func.isNotEmpty(orderTrfRelInfos)){
                List<ReferenceInfo> references = new ArrayList<>();
                orderTrfRelInfos.forEach(orderTrfRelInfo -> {
                    ReferenceInfo referenceInfo = new ReferenceInfo();
                    referenceInfo.setRefSystemId(orderTrfRelInfo.getRefSystemId());
                    referenceInfo.setReferenceNo(orderTrfRelInfo.getRefNo());
                    referenceInfo.setCreateType(orderTrfRelInfo.getCreateType());
                    referenceInfo.setTrfId(orderTrfRelInfo.getId());
                    referenceInfo.setExtObjectId(orderTrfRelInfo.getExtObjectId());
                    referenceInfo.setPackageBarcode(orderTrfRelInfo.getPackageBarcode());
                    referenceInfo.setExternalOrderNo(orderTrfRelInfo.getExternalOrderNo());
                    referenceInfo.setExternalOrderNoSuffix(orderTrfRelInfo.getExternalOrderNoSuffix());
                    header.setReferenceOrderNo(orderTrfRelInfo.getExternalOrderNo());
                    if (Func.isNotEmpty(orderTrfRelInfo.getExtData())) {
                        referenceInfo.setExtData(JSON.parseObject(orderTrfRelInfo.getExtData(), new TypeReference<Map<String, Object>>() {
                        }));
                    }
                    references.add(referenceInfo);
                });
                header.setReferences(references);

            }
            if(Func.isNotEmpty(header.getReferences())){
                List<ReferenceInfo> references = header.getReferences();
                ReferenceInfo referenceInfo = null;
                if(RefSystemIdEnum.check(header.getReferenceId(),RefSystemIdEnum.SubContract)){
                    referenceInfo = references.stream().filter(ref -> RefSystemIdEnum.check(ref.getRefSystemId(),RefSystemIdEnum.SubContract)).findAny().orElse(null);
                }
                if(RefSystemIdEnum.check(header.getReferenceId(),RefSystemIdEnum.ExternalSubContract,RefSystemIdEnum.PortalCustomerNumber)){
                    referenceInfo = references.stream().filter(ref -> RefSystemIdEnum.check(ref.getRefSystemId(),RefSystemIdEnum.ExternalSubContract)).findAny().orElse(null);
                }
                if(RefSystemIdEnum.check(header.getReferenceId(),RefSystemIdEnum.OTS)){
                    referenceInfo = references.stream().filter(ref -> RefSystemIdEnum.check(ref.getRefSystemId(),RefSystemIdEnum.OTS)).findAny().orElse(null);
                }
                if(Func.isNotEmpty(referenceInfo)){
                    header.setReferenceOrderNo(referenceInfo.getExternalOrderNo());
                }
            }*/
            //sameAsApplicant
            header.setSameAsApplicantFlag(slOrder.getSameAsApplicantFlag());
            //
            header.setBossOrderNumber(slOrder.getBossOrderNo());
            header.setBossOrderNumberHtml(getBossOrderNoHtml(order));
            header.setcSName(slOrder.getcSName());
            header.setcSContact(slOrder.getcSContact());
            header.setResponsibleTeamCode(slOrder.getResponsibleTeamCode());
            header.setcSEmail(slOrder.getcSEmail());
            header.setCustomerRemark(slOrder.getCustomerRemark());
            header.setSampleConfirmDate(slOrder.getSampleConfirmDate());
            header.setCuttingExpectDueDate(slOrder.getCuttingExpectDueDate());
            header.setSubcontractExpectDueDate(slOrder.getSubcontractExpectDueDate());
            com.sgs.preorder.facade.model.req.OrderNosReq orderNosReq1 = new com.sgs.preorder.facade.model.req.OrderNosReq();
            orderNosReq1.setOrderNos(Lists.newArrayList(order.getOrderNo()));
            orderNosReq1.setToken(tokenClient.getToken());
            BaseResponse<List<JobListRsp>> jobListRsp = gpnJobFacade.queryJobList(orderNosReq1);
            if (jobListRsp.isSuccess() && Func.isNotEmpty(jobListRsp.getData())) {
                List<JobListRsp> jobListRspList = jobListRsp.getData();
                List<Date> jobExpectedDueDateList = jobListRspList.stream().filter(item -> !JobStatus.check(item.getJobStatus(), JobStatus.Cancelled) && Func.isNotEmpty(item.getExpectedDueDate())).map(JobListRsp::getExpectedDueDate).map(item -> {
                    try {
                        return DateFormatUtil.parseDate(DateFormatUtil.PATTERN_DEFAULT_ON_SECOND, item);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    return null;
                }).filter(Func::isNotEmpty).collect(Collectors.toList());
                jobExpectedDueDateList = jobExpectedDueDateList.stream().filter(Func::isNotEmpty).collect(Collectors.toList());
                if (Func.isNotEmpty(jobExpectedDueDateList)) {
                    Date maxJobExpectedDueDate = jobExpectedDueDateList.stream().max(Date::compareTo).orElse(null);
                    header.setJobExpectDueDate(maxJobExpectedDueDate);
                }
            }

            header.settRFSubmissionDate(slOrder.gettRFSubmissionDate());
            header.setCustomerRefNo(slOrder.getCustomerRefNo());
            header.setTrfTemplateOwner(slOrder.getTrfTemplateOwner());
            header.setIdbLab(slOrder.getIdbLab());

            header.setOr(slOrder.getOr());
            header.setCr(slOrder.getCr());
            header.setDateEditFlag(slOrder.getDateEditFlag());
            header.setCaseType(slOrder.getCaseType());

            header.setBossOrderNo(slOrder.getBossOrderNo());
            header.setDelayType(slOrder.getDelayType());
            header.setDelayReason(slOrder.getDelayReason());
            header.setOperationMode(order.getOperationMode());

            header.setSpecialDiscount(slOrder.getSpecialDiscount());
            header.setQuoteCurrencyId(slOrder.getQuoteCurrencyId());
            header.setQuoteServiceLevel(slOrder.getQuoteServiceLevel());
            header.setCharging_status(slOrder.getCharging_status());
            header.setCustomerReference(slOrder.getCustomerReference());
            //kevin gpo
            List<String> caList = Lists.newArrayList();
            if (Func.isNotEmpty(slOrder.getProductCategory())) {
                caList.add(slOrder.getProductCategory());
            }
            if (Func.isNotEmpty(slOrder.getProductSubCategory())) {
                caList.add(slOrder.getProductSubCategory());
            }
            if (Func.isNotEmpty(caList)) {
                header.setProductCategories(caList.toArray(new String[caList.size()]));
            }
            header.setDueDateConfirmFlag(order.getDueDateConfirmFlag());
            header.setPaymentStatus(order.getPayStatus());
            header.setRefBossInvoiceNo(slOrder.getRefBossInvoiceNo());
            header.setStarlimsFolderNo(slOrder.getStarlimsFolderNo());
            header.setEnquiryNo(slOrder.getEnquiryNo());
            header.setFurtherInformationReceivingDate(slOrder.getFurtherInformationReceivingDate());
            header.setSampleResubmissionDate(slOrder.getSampleResubmissionDate());

            header.setSalesPerson(slOrder.getSalesPerson());
            header.setTsPerson(slOrder.getTSPerson());
            header.setProject(slOrder.getProject());
            header.setKA(slOrder.getKA());
            header.setRootBuCode(order.getRootBuCode());

            SubContractInfo objSubContractInfo = subContractClient.getSubContractByOrderNo(order.getOrderNo());
            int subcontractOrderFlag = objSubContractInfo == null ? 0 : 1;
            header.setSubcontractOrderFlag(subcontractOrderFlag);
            //
            List<CrossLabRelPO> crossLabRelPOS = crossLabService.getToLabByOrder(order.getOrderNo());
            if (CollectionUtils.isNotEmpty(crossLabRelPOS)) {
                header.setShareToLab(crossLabRelPOS.get(0).getToLab());
            }


            header.setSubcontractFee(slOrder.getSubcontractFee());
            header.setSubcontractFeeCurrency(slOrder.getSubcontractFeeCurrency());
//            if(OperationType.check(order.getOperationType(),OperationType.SubContract)&&objSubContractInfo!=null){
//                header.setSubcontractFee(objSubContractInfo.getSubcontractFee());
//                header.setSubcontractFeeCurrency(objSubContractInfo.getSubcontractFeeCurrency());
//            }
            header.setOtsSubcontractNo(slOrder.getOtsSubcontractNo());
            header.setNeedToBossFlag(order.getNeedToBossFlag());


        }
        // endregion
        // toBossFlag
        header.setToBossFlag(Func.isNotEmpty(orderDetailMapper.checkBossFlag(orderId)) ? 1 : 0);
        OrderExtDTO orderExt = orderExtService.getOrderExtByOrderId(header.getOrderId());
        if(ObjectUtil.isNotEmpty(orderExt)&&ObjectUtil.isNotEmpty(orderExt.getExtFields())){
            OrderExtTrfTemplateDTO trfTemplate = orderExt.getExtFields().getTrfTemplate();
            header.setTemplateId(trfTemplate.getTemplateId());
            header.setTemplateName(trfTemplate.getTemplateName());
        }
        // Splitted Ref. Number
        Integer orderCategory = order.getOrderCategory();
        if (orderCategory != null && orderCategory == 3 && StringUtils.isNotBlank(order.getOrderNo())) {
            order.setSplitRefs(orderDetailMapper.getSplitOrderRefList(order.getOrderNo()));
        }
        orderDetail.setSplitRefs(order.getSplitRefs());
        header.setParcelNo(order.getParcelNos());
        header.setOldParcelNo(header.getParcelNo());
        //external Order
        GpoExternalOrderNoReq externalOrderNoReq = new GpoExternalOrderNoReq();
        externalOrderNoReq.setOrderNoList(Lists.newArrayList(header.getOrderNo()));
        externalOrderNoReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        List<GpoExternalOrderInfo> gpoExternalOrderInfos = externalNoService.queryExternalOrder(externalOrderNoReq).getData();
        if (Func.isNotEmpty(gpoExternalOrderInfos)) {
            orderDetail.setExternalOrderInfo(gpoExternalOrderInfos.get(0));
            header.setExternalOrderNo(gpoExternalOrderInfos.get(0).getExternalOrderNo());
        }
        // to cp Customer Portal Reference Number
        /*if(Func.isNotEmpty(orderTrfRelInfo)){
            header.setProtoReferenceNo(orderTrfRelInfo.getRefNo());
            header.setRefSystemId(orderTrfRelInfo.getRefSystemId());
        }*/
        com.sgs.preorder.facade.model.req.OrderNosReq gpoOrderNosReq = new com.sgs.preorder.facade.model.req.OrderNosReq();
        gpoOrderNosReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        gpoOrderNosReq.setOrderNos(Arrays.asList(order.getOrderNo()));
        gpoOrderNosReq.setToken(tokenClient.getToken());
        BaseResponse<List<JobListRsp>> jobListRspBaseResponse = gpnJobFacade.queryJobList(gpoOrderNosReq);
        long totalExecutionCount = 0l;
        if (Func.isNotEmpty(jobListRspBaseResponse) && jobListRspBaseResponse.isSuccess()) {
            List<JobListRsp> jobList = jobListRspBaseResponse.getData();
            if (Func.isNotEmpty(jobList)) {
                long jobCount = jobList.stream().filter(job -> JobStatus.checkCategory(job.getJobStatus(), SGSConstant.STATUS_CATEGORY.INVALID)).count();
                totalExecutionCount += jobCount;
            }
        }
        QuerySubContractListReq querySubContractListReq = new QuerySubContractListReq();
        querySubContractListReq.setOrderNo(order.getOrderNo());
        querySubContractListReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        querySubContractListReq.setToken(tokenClient.getToken());
        BaseResponse<List<SubContractExternalRelationshipDTO>> subcontractRsp = subContractFacade.querySubContractList(querySubContractListReq);
        if (Func.isNotEmpty(subcontractRsp) && subcontractRsp.isSuccess()) {
            List<SubContractExternalRelationshipDTO> subContractExternalRelationshipDTOS = subcontractRsp.getData();
            if (Func.isNotEmpty(subContractExternalRelationshipDTOS)) {
                long subcontractCount = subContractExternalRelationshipDTOS.stream().filter(subcontract -> !SubContractStatusEnum.check(subcontract.getStatus(), SubContractStatusEnum.Cancelled)).count();
                totalExecutionCount += subcontractCount;
            }
        }
        // 判断当前订单是Share To的场景
        header.setShare(Func.isNotEmpty(header.getShareToLab()) && !Func.equalsSafe(header.getLabCode(), userLabBu.getLabCode()));
        header.setTopsToDisabled(totalExecutionCount > 0 || (Func.isNotEmpty(header.getShareToLab()) && !Func.equalsSafe(header.getLabCode(), userLabBu.getLabCode())));
        orderDetail.setHeaders(header);
        // Customer
        CustomerType.setCustomer(orderDetail, order.getCustomers());

        // OrderPersons
        OrderPersonType.setOrderPerson(orderDetail, order.getOrderPersons());
        CustomerInfo subcontractFrom = orderDetail.getSubcontractFrom();
        if (Func.isNotEmpty(subcontractFrom)) {
            header.setIdbLab(subcontractFrom.getCustomerId());
        }
        // tb_report
        orderDetail.setReport(order.getReport());
        orderDetail.setReports(reportService.getNotesReportListByOrderNos(Arrays.asList(order.getOrderNo())));
        com.sgs.otsnotes.facade.model.req.report.OrderNosReq orderNosReq = new com.sgs.otsnotes.facade.model.req.report.OrderNosReq();
        orderNosReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        orderNosReq.setOrderNoList(Arrays.asList(order.getOrderNo()));
        reportFacade.getReportByOrderNos(orderNosReq);
        // renew 状态
//        orderDetail.set

        // SERVICE REQUIREMENT
        TestRequestInfo testRequest = order.getTestRequest();
        if (testRequest == null) {
            testRequest = new TestRequestInfo();
        } else {
            testRequest.setTestRequestContactsInfos(getRequestContacts(orderId));
        }
        OrderReportReceiverInfo receiver = order.getOrderReportReceiver();
        if (receiver != null) {
            testRequest.setReceiverId(receiver.getReceiverId());
            testRequest.setReportHeader(receiver.getReportHeader());
            testRequest.setReportDeliveredTo(receiver.getReportDeliveredTo());
            testRequest.setReportLock(receiver.getReportInfoLock());
        } else {
            testRequest.setReportLock(ReportInfoLockEnum.UN_LOCK.getStatus());
        }
        //
        this.setTestRequestContactsInfo(order.getContacts(), testRequest);
        testRequest.setIsProformaInvoice(testRequest.getProformaInvoice());
        orderDetail.setTestRequest(testRequest);

        // Others
        OrderDetailOtherInfo other = new OrderDetailOtherInfo();
        if (slOrder != null) {
            other.setSlOrderId(slOrder.getSlOrderId());
            other.setSoftCopyDeliveryDate(slOrder.getSoftCopyDeliveryDate());
            other.setReportEmailBy(slOrder.getReportEmailBy());
            other.setHardCopyDeliveryDate(slOrder.getHardCopyDeliveryDate());
            other.setHardcopyDeliveryBy(slOrder.getHardcopyDeliveryBy());
            other.setDeliveryRemark(slOrder.getDeliveryRemark());
        }
        // 查询order下是否有配置的tag
        ObjectTagInfoExample example = new ObjectTagInfoExample();
        ObjectTagInfoExample.Criteria criteria = example.createCriteria();
        criteria.andObjectIdEqualTo(orderId);
        List<ObjectTagInfoPO> tagInfoPOList = objectTagInfoMapper.selectByExample(example);
        if (Func.isNotEmpty(tagInfoPOList) && needTag) {
            ObjectTagInfoPO tagInfo = tagInfoPOList.get(0);
            header.setTagValueSelectDTOS(convertTagValues(order.getBuCode(), tagInfo.getData().toString(), TagObjectType.Order.getValue(), null));
        }
        if (Func.isNotEmpty(tagInfoPOList)) {
            ObjectTagInfoPO tagInfo = tagInfoPOList.get(0);
            List<TagValueSelectDTO> savedTags = JSONObject.parseObject(tagInfo.getData().toString(), new TypeReference<List<TagValueSelectDTO>>() {
            });
            if (Func.isNotEmpty(savedTags)) {
                savedTags = savedTags.stream().sorted(Comparator.comparing(TagValueSelectDTO::getTagId)).collect(Collectors.toList());
                other.setTagValues(savedTags);
            }
        }
        other.setExpressNo(order.getExpressNo());
        orderDetail.setOther(other);

        //获取sampleNoRule
        BuParamValueRsp objBuParamValueRsp1 = this.getSamleNoRule(this.getBuCodeForSampleNoRule(header));
        if (objBuParamValueRsp1 != null) {
            orderDetail.setSampleNoRule(objBuParamValueRsp1.getParamValue());
        } else {
            rspResult.setMsg("Get Bu Param is failed for SampleNoRule .");
            return rspResult;
        }
        //找主单formId
        doSetHostFormId(header);
        //校验订单下，及订单的子单下  是否存在不是New的报告
        OrderNosReq nosReq = new OrderNosReq();
        nosReq.setOrderNo(order.getOrderNo());
        nosReq.setReportRequirement(orderDetail.getTestRequest().getReportRequirement());
        BaseResponse<List<String>> havingPdfReportFlag = reportFacade.getDisabledSampleNo(nosReq);
        orderDetail.setReportInfoDisable(false);
        if (Func.isNotEmpty(havingPdfReportFlag) && Func.isNotEmpty(havingPdfReportFlag.getData())
                && Func.equals(ResponseCode.SUCCESS.getCode(), havingPdfReportFlag.getStatus())
        ) {
            orderDetail.setReportInfoDisable(true);
        }

        // 版本
        OrderVersionInfo version = new OrderVersionInfo();
        logger.debug("Order Old Info: {}", JSON.toJSONString(header));
        logger.debug("Order other Old Info: {}", JSON.toJSONString(other));
        version.setOldOrderVersionId(header.getOrderHashCode(other));
        version.setOldSLOrderVersionId(header.getSlOrderHashCode(other));
        logger.debug("Order Parcel Old Info: {}", JSON.toJSONString(header.getParcelNo()));
        version.setOldParcelVersionId(header.getParcelHashCode());
        logger.debug("Order testRequest Old Info: {}", JSON.toJSONString(testRequest));
        version.setOldTestRequestVersionId(testRequest.hashCode());
        version.setOldReceiverVersionId(testRequest.getHashCode());
        version.setOldTrfRelationshipVersionId(header.getTrfRelationshipHashCode());
//        version.setOldTopsVersionId(header.getTopsHashCode());

        version.setOldToTestVersionId(NumberUtil.toInt(order.getToTestVersion()));
        orderDetail.setVersion(version);

        rspResult.setData(orderDetail);
        rspResult.setSuccess(true);
        stopWatch.stop();
        logger.info("OrderDetailService.getOrderDetailInfo 请求接口耗时：{}.", stopWatch.getTime());

        return rspResult;
    }

    /**
     * 查询order header
     * 简化getOrderDetailInfo查询
     * @param orderId
     * @return
     */
    public CustomResult getOrderHeaderInfo(String orderId) {
        return getOrderHeaderInfo(orderId, tokenClient.getToken());
    }

    /**
     * getOrderHeaderInfo
     *
     * @param orderId
     * @return
     */
    public CustomResult getOrderHeaderInfo(String orderId, String token) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        CustomResult rspResult = new CustomResult();
        OrderDetailInfo order = orderDetailMapper.getOrderHeaderInfo(orderId);
        if (order == null) {
            rspResult.setMsg("未找到对应的orderId信息.");
            return rspResult;
        }

        OrderDetailDto orderDetail = new OrderDetailDto();
        UserLabBuInfo userLabBu = null;
        if (Func.isNotEmpty(token)) {
            userLabBu = userClient.getUserLabBuInfo(token);
            orderDetail.setLab(userLabBu);
        }
        OrderHeaderInfo header = new OrderHeaderInfo();
        LabInfoPO lab = labMapper.getLabInfoByOrderId(order.getOrderId());
        if (lab != null) {
            header.setLabId(lab.getLabID());
            header.setLabCode(lab.getLabCode());
        }
        BeanUtils.copyProperties(order, header);
        header.setGroupId(order.getGroupID());
        header.setNewOrderId(order.getOrderId());
        header.setActualStartDate(order.getServiceStartDate());
        header.setOrderStatusText(OrderStatus.getMessage(order.getOrderStatus()));
        header.setOrderConfirmDate(order.getOrderConfirmDate());
        OperationType operationType = OperationType.findCode(order.getOperationType());
        if (operationType != null) {
            header.setOperationTypeDesc(operationType.getCode());
        }
        SlOrderInfoPO slOrder = slOrderMapper.getSlOrderByOrderId(order.getOrderId());
        if (slOrder != null) {
            header.setSlOrderId(slOrder.getSlOrderId());
            //sameAsApplicant
            header.setSameAsApplicantFlag(slOrder.getSameAsApplicantFlag());
            header.setBossOrderNumber(slOrder.getBossOrderNo());
            header.setcSName(slOrder.getcSName());
            header.setcSContact(slOrder.getcSContact());
            header.setResponsibleTeamCode(slOrder.getResponsibleTeamCode());
            header.setcSEmail(slOrder.getcSEmail());
            header.setCustomerRemark(slOrder.getCustomerRemark());
            header.setSampleConfirmDate(slOrder.getSampleConfirmDate());
            header.setCuttingExpectDueDate(slOrder.getCuttingExpectDueDate());
            header.setSubcontractExpectDueDate(slOrder.getSubcontractExpectDueDate());
            header.settRFSubmissionDate(slOrder.gettRFSubmissionDate());
            header.setCustomerRefNo(slOrder.getCustomerRefNo());
            header.setTrfTemplateOwner(slOrder.getTrfTemplateOwner());
            header.setIdbLab(slOrder.getIdbLab());
            header.setOr(slOrder.getOr());
            header.setCr(slOrder.getCr());
            header.setDateEditFlag(slOrder.getDateEditFlag());
            header.setCaseType(slOrder.getCaseType());
            header.setBossOrderNo(slOrder.getBossOrderNo());
            header.setDelayType(slOrder.getDelayType());
            header.setDelayReason(slOrder.getDelayReason());
            header.setSpecialDiscount(slOrder.getSpecialDiscount());
            header.setQuoteCurrencyId(slOrder.getQuoteCurrencyId());
            header.setQuoteServiceLevel(slOrder.getQuoteServiceLevel());
            header.setCharging_status(slOrder.getCharging_status());
            header.setCustomerReference(slOrder.getCustomerReference());
            List<String> caList = Lists.newArrayList();
            if (Func.isNotEmpty(slOrder.getProductCategory())) {
                caList.add(slOrder.getProductCategory());
            }
            if (Func.isNotEmpty(slOrder.getProductSubCategory())) {
                caList.add(slOrder.getProductSubCategory());
            }
            if (Func.isNotEmpty(caList)) {
                header.setProductCategories(caList.toArray(new String[caList.size()]));
            }
            header.setPaymentStatus(order.getPayStatus());
            header.setRefBossInvoiceNo(slOrder.getRefBossInvoiceNo());
            header.setStarlimsFolderNo(slOrder.getStarlimsFolderNo());
            header.setEnquiryNo(slOrder.getEnquiryNo());
            header.setFurtherInformationReceivingDate(slOrder.getFurtherInformationReceivingDate());
            header.setSampleResubmissionDate(slOrder.getSampleResubmissionDate());
            header.setSalesPerson(slOrder.getSalesPerson());
            header.setTsPerson(slOrder.getTSPerson());
            header.setProject(slOrder.getProject());
            header.setKA(slOrder.getKA());
            header.setSubcontractFee(slOrder.getSubcontractFee());
            header.setSubcontractFeeCurrency(slOrder.getSubcontractFeeCurrency());
            header.setOtsSubcontractNo(slOrder.getOtsSubcontractNo());
        }

        orderDetail.setHeaders(header);
        rspResult.setData(orderDetail);
        rspResult.setSuccess(true);
        stopWatch.stop();
        logger.info("OrderDetailService.getOrderHeaderInfo 请求接口耗时：{}.", stopWatch.getTime());

        return rspResult;
    }

    private boolean isInterfaceRefSystemSource(List<ReferenceInfo> referenceInfoList) {
        boolean result = false;
        List<Integer> sciTrfSystemList = sciTrfService.listSciTrfRefSystem();
        List<Integer> apiRefSystem = Lists.newArrayList(RefSystemIdEnum.TIC.getRefSystemId());
        sciTrfSystemList.add(RefSystemIdEnum.SGSMart.getRefSystemId());
        apiRefSystem.addAll(sciTrfSystemList);
        if (Func.isNotEmpty(referenceInfoList)) {
            for (ReferenceInfo referenceInfo : referenceInfoList) {
                if (apiRefSystem.contains(referenceInfo.getRefSystemId()) || RefIntegrationChannel.check(referenceInfo.getIntegrationChannel(),RefIntegrationChannel.SCI)) {
                    result = true;
                    break;
                }
            }
        }
        return result;
    }

    private void doSetHostFormId(OrderHeaderInfo header) {
        if (Func.isNotEmpty(header.getOldOrderNo()) && OperationType.check(header.getOperationType(), OperationType.SubContract, OperationType.NewSubContract, OperationType.LightSubContract)) {
            OrderInfoPO objOrderInfoPO = orderMapper.getInfoByOrderNo(header.getOldOrderNo());
            if (objOrderInfoPO != null) {
                List<ProductInstanceDTO> productInstanceDTOList = productMapper.getProductInfoAllByOrderId(objOrderInfoPO.getID());
                ProductInstanceDTO gridDff = productInstanceDTOList.stream().filter(e -> Func.isNotEmpty(e.getHeaderID())).findFirst().orElse(null);
                ProductInstanceDTO headDff = productInstanceDTOList.stream().filter(e -> Func.isEmpty(e.getHeaderID())).findFirst().orElse(null);
                if (headDff != null) {
                    header.setHostDffFormId(headDff.getdFFFormID());
                }
                if (gridDff != null) {
                    header.setHostDffGridId(gridDff.getdFFFormID());
                }
            }
        }
    }

    private String getBuCodeForSampleNoRule(OrderHeaderInfo header) {
        String buCode = header.getBuCode();
        //8,5,6直接返回主单bu
        if (OperationType.check(header.getOperationType(), OperationType.SubContract, OperationType.NewSubContract, OperationType.LightSubContract)) {
            LabDTO labDTO = new LabDTO();
            labDTO.setLabCode(header.getIdbLab());
            List<LabInfo> labInfoList = frameWorkClient.getLabInfo(labDTO);
            if (Func.isNotEmpty(labInfoList)) {
                buCode = labInfoList.get(0).getProductLineAbbr();
            }
        }
        return buCode;
    }

    public BuParamValueRsp getSamleNoRule(String productLineCode) {
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setProductLineCode(productLineCode);
        buParamReq.setGroupCode(Constants.BU_PARAM.SAMPLENORULE.GROUP);
        buParamReq.setParamCode(Constants.BU_PARAM.SAMPLENORULE.SAMPLENO_RULE.CODE);
        List<BuParamValueRsp> buParamValueDTOS = frameWorkClient.getBuParams(buParamReq);
        if (Func.isNotEmpty(buParamValueDTOS)) {
            return buParamValueDTOS.get(0);
        } else {
            return null;
        }
    }

    public List<DffCustomizeColumn> getDffCustomizeColumns(String productLineCode) {
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setProductLineCode(productLineCode);
        buParamReq.setGroupCode(Constants.BU_PARAM.DFF.GROUP);
        buParamReq.setParamCode(Constants.BU_PARAM.DFF.DFFCUSTOMIZECOLUMNS.CODE);
        List<BuParamValueRsp> buParamValueDTOS = frameWorkClient.getBuParams(buParamReq);
        if (Func.isNotEmpty(buParamValueDTOS)) {
            List<DffCustomizeColumn> dffCustomizeColumns = JSON.parseArray(buParamValueDTOS.get(0).getParamValue(), DffCustomizeColumn.class);
            return dffCustomizeColumns;
        } else {
            return Lists.newArrayList();
        }
    }

    public List<TagValueSelectDTO> convertTagValues(String productLineCode, String jsonStr, String tagObjectType, List<TagSearchDTO> allTags) {
        List<TagValueSelectDTO> tags = Lists.newArrayList();
        String buCode = productLineCode;
        // 查询当前BU配置的完整的Tag
        if (Func.isEmpty(allTags)) {
            allTags = tagService.searchTagList(buCode, tagObjectType, null);
        }
        if (Func.isEmpty(allTags)) {
            return tags;
        }
        // 当前数据库保存的Tag
        Map<String, List<String>> selectedValueMap = Maps.newHashMap();
        if (Func.isNotEmpty(jsonStr)) {
            List<TagValueSelectDTO> savedTags = JSONObject.parseObject(jsonStr, new TypeReference<List<TagValueSelectDTO>>() {
            });
            if (Func.isNotEmpty(savedTags)) {
                selectedValueMap = savedTags.stream().collect(Collectors.toMap(TagValueSelectDTO::getTagId, TagValueSelectDTO::getSelectedValue));
            }
        }
        Map<String, List<String>> finalSelectedValueMap = selectedValueMap;
        allTags.stream().forEach(t -> {
            TagValueSelectDTO tag = new TagValueSelectDTO();
            tag.setTagId(t.getTagId());
            tag.setTagName(t.getTagName());
            tag.setIsCheck(Func.equalsSafe(t.getIsCheck(), Byte.valueOf("1")));
            tag.setIsHand(Func.equalsSafe(t.getIsHand(), Byte.valueOf("1")));
            tag.setIsSave(Func.equalsSafe(t.getIsSave(), Byte.valueOf("1")));
            tag.setIsRequired(t.getIsRequired());
            if (finalSelectedValueMap.containsKey(tag.getTagId())) {
                tag.setSelectedValue(finalSelectedValueMap.get(tag.getTagId()));
            }
            tag.setTagValues(t.getTagValues());
            tags.add(tag);
        });
        return tags;
    }

    /**
     * @param contacts
     * @param testRequest
     */
    private void setTestRequestContactsInfo(List<TestRequestInfoContactsInfo> contacts, TestRequestInfo testRequest) {
        if (contacts == null || contacts.isEmpty() || testRequest == null) {
            return;
        }
        List<TestRequestContactsInfo> testRequestContactsInfos = Lists.newArrayList();
        for (TestRequestInfoContactsInfo contact : contacts) {
            TestRequestContactsInfo testRequestContactsInfo = new TestRequestContactsInfo();
            Func.copy(contact, testRequestContactsInfo);
            testRequestContactsInfos.add(testRequestContactsInfo);
            ContactsType contactsType = ContactsType.getType(contact.getContactsType());
            if (contactsType == null || StringUtils.isBlank(contact.getDeliverTo())) {
                continue;
            }
            List<String> deliverIds = Lists.newArrayList(StringUtils.split(contact.getDeliverTo(), ","));
            CopyDeliverType applicant = CopyDeliverType.Applicant;
            CopyDeliverType payer = CopyDeliverType.Payer;
            switch (contactsType) {
                case SoftCopy:
                    testRequest.setSoftCopyToApplicantFlag(NumberUtil.equals(deliverIds, applicant.getStatus()));
                    testRequest.setSoftCopyToPayerFlag(NumberUtil.equals(deliverIds, payer.getStatus()));
                    testRequest.setSoftCopyToOther(contact.getDeliverOthers());
                    break;
                case HardCopy:
                    testRequest.setHardCopyToApplicantFlag(NumberUtil.equals(deliverIds, applicant.getStatus()));
                    testRequest.setHardCopyToPayertFlag(NumberUtil.equals(deliverIds, payer.getStatus()));
                    testRequest.setHardCopyToOther(contact.getDeliverOthers());
                    break;
            }
        }
        //Other中填写的内容为空时，不需要保存Other选项
        for (TestRequestContactsInfo testRequestContactsInfo : testRequestContactsInfos) {
            String deliverTo = testRequestContactsInfo.getDeliverTo();
            String deliverToOthers = testRequestContactsInfo.getDeliverOthers();
            if((Func.isEmpty(deliverToOthers) || Func.isEmpty(deliverToOthers.trim())) && Func.isNotEmpty(deliverTo)){
                List<Integer> deliverIds = Func.toStrList(deliverTo).stream().map(Func::toInteger).collect(Collectors.toList());
                deliverIds.removeIf(n -> Func.isNotEmpty(n) && n == 5);
                testRequestContactsInfo.setDeliverTo(Func.join(deliverIds));
            }
        }
        testRequest.setTestRequestContactsInfos(testRequestContactsInfos);
    }

    private List<TestRequestContactsInfo> getRequestContacts(String orderId) {
        TestRequestContactsInfo testRequestContactsInfo = new TestRequestContactsInfo();
        TestRequestContactsInfoExample objTestRequestContactsInfoExample = new TestRequestContactsInfoExample();
        objTestRequestContactsInfoExample.createCriteria().andOrderIdEqualTo(orderId);
        List<TestRequestContactsInfoPO> testRequestContactsInfoPOS = testRequestContactsInfoMapper.selectByExample(objTestRequestContactsInfoExample);
        List<TestRequestContactsInfo> testRequestContactsInfoList = Lists.newArrayList();
        testRequestContactsInfoPOS.forEach(e -> {
            TestRequestContactsInfo testRequestContactsInfo1 = new TestRequestContactsInfo();
            Func.copy(e, testRequestContactsInfo1);
            testRequestContactsInfoList.add(testRequestContactsInfo1);
        });
        return testRequestContactsInfoList;
      /*  if(requestContactsInfoSoftCopyTo!=null){
            testRequestContactsInfo.setSoftCopyToContactsList(requestContactsInfoSoftCopyTo.getDeliverTo());
            testRequestContactsInfo.setSoftCopyToOthers(requestContactsInfoSoftCopyTo.getDeliverOthers());
        }

        TestRequestContactsInfoPO requestContactsInfoSoftCopyCc=testRequestContactsInfoPOS.stream().filter(contactsInfoPO -> contactsInfoPO.getContactsType()== TestRequestContactsType.SOFTCOPYCC.getCode()).findFirst().orElse(null);
        if(requestContactsInfoSoftCopyCc!=null){
            testRequestContactsInfo.setSoftcopyCcList(requestContactsInfoSoftCopyCc.getDeliverTo());
            testRequestContactsInfo.setSoftcopyCcOthers(requestContactsInfoSoftCopyCc.getDeliverOthers());
        }

        TestRequestContactsInfoPO requestContactsInfoHardCopy=testRequestContactsInfoPOS.stream().filter(contactsInfoPO -> contactsInfoPO.getContactsType()== TestRequestContactsType.HARDCOPYTO.getCode()).findFirst().orElse(null);
        if(requestContactsInfoHardCopy!=null){
            testRequestContactsInfo.setHardCopyToContactsList(requestContactsInfoHardCopy.getDeliverTo());
            testRequestContactsInfo.setHardCopyToOthers(requestContactsInfoHardCopy.getDeliverOthers());
        }

        TestRequestContactsInfoPO requestContactsInfoInvoice=testRequestContactsInfoPOS.stream().filter(contactsInfoPO -> contactsInfoPO.getContactsType()== TestRequestContactsType.INVOICE.getCode()).findFirst().orElse(null);
        if(requestContactsInfoInvoice!=null){
            testRequestContactsInfo.setInvoiceToList(requestContactsInfoInvoice.getDeliverTo());
            testRequestContactsInfo.setInvoiceToOthers(requestContactsInfoInvoice.getDeliverOthers());
        }


        TestRequestContactsInfoPO requestContactsInfoPRELAM=testRequestContactsInfoPOS.stream().filter(contactsInfoPO -> contactsInfoPO.getContactsType()== TestRequestContactsType.PRELAM.getCode()).findFirst().orElse(null);
        if(requestContactsInfoPRELAM!=null){
            testRequestContactsInfo.setPrelamToList(requestContactsInfoPRELAM.getDeliverTo());
            testRequestContactsInfo.setPrelamToOthers(requestContactsInfoPRELAM.getDeliverOthers());
        }

        TestRequestContactsInfoPO requestContactsInfoPRELAMCC=testRequestContactsInfoPOS.stream().filter(contactsInfoPO -> contactsInfoPO.getContactsType()== TestRequestContactsType.PRELAMCC.getCode()).findFirst().orElse(null);
        if(requestContactsInfoPRELAMCC!=null){
            testRequestContactsInfo.setPrelamCcList(requestContactsInfoPRELAMCC.getDeliverTo());
            testRequestContactsInfo.setPrelamCcOthers(requestContactsInfoPRELAMCC.getDeliverOthers());
        }

*//*        TestRequestContactsInfoPO requestContactsInfoREPORT=testRequestContactsInfoPOS.stream().filter(contactsInfoPO -> contactsInfoPO.getContactsType()== TestRequestContactsType.REPORT.getCode()).findFirst().orElse(null);
        if(requestContactsInfoREPORT!=null){
            testRequestContactsInfo.setReportToContactsList(requestContactsInfoREPORT.getDeliverTo());
            testRequestContactsInfo.setReportToOthers(requestContactsInfoREPORT.getDeliverOthers());
        }*//*

        TestRequestContactsInfoPO requestContactsInfoRETURNSAMPLE=testRequestContactsInfoPOS.stream().filter(contactsInfoPO -> contactsInfoPO.getContactsType()== TestRequestContactsType.RETURNSAMPLE.getCode()).findFirst().orElse(null);
        if(requestContactsInfoRETURNSAMPLE!=null){
            testRequestContactsInfo.setReturnSampleToList(requestContactsInfoRETURNSAMPLE.getDeliverTo());
            testRequestContactsInfo.setReturnSampleToOthers(requestContactsInfoRETURNSAMPLE.getDeliverOthers());
        }*/
    }

    private String getBossOrderNoHtml(OrderDetailInfo order) {
        List<BossOrderInfo> bossOrderInfos = order.getBossOrderInfos();
        if (CollectionUtils.isNotEmpty(bossOrderInfos)) {
            List<String> bossOrderNoHtml = Lists.newArrayList();
            for (BossOrderInfo bossOrder : bossOrderInfos) {
                if (Func.equalsSafe(bossOrder.getStatus(), String.valueOf(BossOrderStatusEnum.CANCELLED.getStatus())) ||
                        Func.equalsSafe(bossOrder.getStatus(), String.valueOf(BossOrderStatusEnum.DRAFT.getStatus())) ||
                        Func.equalsSafe(bossOrder.getStatus(), String.valueOf(BossOrderStatusEnum.REJECTED.getStatus()))) {
                    bossOrderNoHtml.add(String.format("<span style='color:#8a8282;'>%s</span>", bossOrder.getTrueOrderNo()));
                    continue;
                }
                if (StringUtils.isNotBlank(bossOrder.getTrueOrderNo())) {
                    bossOrderNoHtml.add(String.format("<span>%s</span>", bossOrder.getTrueOrderNo()));
                }
            }
            bossOrderNoHtml = bossOrderNoHtml.stream().distinct().collect(Collectors.toList());
            return StringUtils.join(bossOrderNoHtml, ",");
        }
        return "";
    }

    /**
     * @param reqObject
     * @return
     */
    public CustomResult saveOrderInfo(OrderDetailDto reqObject) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        CustomResult rspResult = new CustomResult();

        String token = reqObject.getToken();
        if (StringUtils.isBlank(token)) {
            rspResult.setMsg("当前请求的用户Token无效.");
            return rspResult;
        }

        if (!checkDff(reqObject)) {
            rspResult.setMsg("No Chinese Template!");
            return rspResult;
        }

        rspResult = testInfoValidator.validate(reqObject);
        if (!rspResult.isSuccess()) {
            return rspResult;
        }
        //trf 信息验证
        rspResult = trfInfoValidator.validateCreateOrder(reqObject);
        if (!rspResult.isSuccess()) {
            return rspResult;
        }
        if(Func.isEmpty(reqObject.getBuObjectTemplate())){
//        // 根据enquiry查询订单主体信息，基于object设置默认值跟必填校验
            BuObjectTemplateAllDTO buObjects = null;
            BuObjectTemplateDTO buObjectTemplateDTO = new BuObjectTemplateDTO();
            buObjectTemplateDTO.setLabCode(SecurityUtil.getLabCode());
            buObjectTemplateDTO.setObject(com.sgs.preorder.core.constants.Constants.OBJECT.ORDER.CODE);
            buObjectTemplateDTO.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            buObjectTemplateDTO.setNeedBuParam(true);
            if (Func.equals(OperationModeEnum.ChargeOnly.getOperationMode(),reqObject.getHeaders().getOperationMode())){
                buObjectTemplateDTO.setObject("orderV2");
                buObjectTemplateDTO.setOrderModel("ChargeOrder");
                buObjects = buSettingService.getTemplateSettingListV2(buObjectTemplateDTO);
            }else {
                buObjects = buSettingService.getTemplateSettingList(buObjectTemplateDTO);
            }
            reqObject.setBuObjectTemplate(buObjects);
        }
        //logger.info("本次保存订单数据[{}]：{}",reqObject.getHeaders().getOrderNo(), JSON.toJSONString(reqObject));
        String reqData = JSON.toJSONString(reqObject);
        String regionAccount = "";
        List<ReferenceInfo> references = reqObject.getHeaders().getReferences();
        if(Func.isEmpty(references)){
            references = new ArrayList<>();
        }
        reqObject.getHeaders().setReferences(references);

        //判断parcelNo是否为空和新增
        Boolean newParcelNo = false;
        if (Func.isNotEmpty(reqObject.getHeaders().getParcelNo()) && StringUtils.isBlank(reqObject.getHeaders().getOrderId())) {
            newParcelNo = true;
        } else if (Func.isNotEmpty(reqObject.getHeaders().getParcelNo())) {
            //查询之前订单是否关联parcelNo
            int parcelCount = parcelMapper.getCountByOrderId(reqObject.getHeaders().getOrderId());
            if (parcelCount == 0) {
                newParcelNo = true;
            }
        }
        logger.info("newParcelNo:{}", newParcelNo);
        SysParamInfo reqParams = new SysParamInfo();
        try {
            OrderHeaderInfo header = reqObject.getHeaders();
            SlOrderInfoPO oldSlOder = null;
//            String oldTrfNos = null;
            if (header != null && StringUtils.isNotBlank(header.getOrderId())) {
                oldSlOder = slOrderMapper.getSlOrderByOrderId(header.getOrderId());
//                List<OrderTrfRelInfo> oldTrfRels = orderTrfRelationshipMapper.getTrfRelationshipByOrderId(header.getOrderId());
//                if (Func.isNotEmpty(oldTrfRels)) {
//                    oldTrfNos = oldTrfRels.stream().filter(e->Func.isNotEmpty(e.getRefNo())).map(OrderTrfRelInfo::getRefNo).sorted().collect(Collectors.joining(StringPool.COMMA));
//                }
            }
            String newBossOrderNo = null, newRefBossInvoiceNo = null;
            String newTrfNos = null;
            if (header != null) {
                reqParams.setOrderNo(header.getOrderNo());
                //BossOrderNo,RefBossInvoiceNo统一在saveRefBossOrder中处理
                newBossOrderNo = header.getBossOrderNo();
                newRefBossInvoiceNo = header.getRefBossInvoiceNo();
                if (Func.isNotEmpty(oldSlOder)) {
                    boolean isChange = Func.isNotEmpty(newBossOrderNo) && !Func.equalsSafe(Func.toStr(newBossOrderNo), Func.toStr(oldSlOder.getBossOrderNo())) || !Func.equalsSafe(Func.toStr(newRefBossInvoiceNo), Func.toStr(oldSlOder.getRefBossInvoiceNo()));
                    if (isChange) {
                        header.setBossOrderNo(null);
                        header.setRefBossInvoiceNo(null);
                    }
                }
                if(Func.isEmpty(header.getToDMFlag())){
                    header.setToDMFlag(0);
                }
                //校验 ServiceLevel 是否合法
                if(Func.isEmpty(header.getServiceLevel())){
                    rspResult.setSuccess(false);
                    rspResult.setMsg("Service Type can't be empty!");
                    return rspResult;
                }
                String serviceLevel = header.getServiceLevel().toString();
                if(Func.isEmpty(ServiceLevelEnum.getServiceLevel(serviceLevel))){
                    rspResult.setSuccess(false);
                    rspResult.setMsg("Service Type is Invalidate!");
                    return rspResult;
                }
//                List<ReferenceInfo> referenceInfos = header.getReferences();
//                if(Func.isNotEmpty(referenceInfos)) {
//                    newTrfNos = referenceInfos.stream().filter(e->Func.isNotEmpty(e.getReferenceNo())).map(ReferenceInfo::getReferenceNo).sorted().collect(Collectors.joining(StringPool.COMMA));
//                }
            }
            UserInfo user = tokenClient.getUser(token);
            if (user == null) {
                rspResult.setMsg("当前请求的用户Token无效.");
                return rspResult;
            }
            UserLabBuInfo userLabBu = userClient.getUserLabBuInfo(token);

            UserHelper.setLocalUser(user);

            regionAccount = user.getRegionAccount();
            reqParams.setUserName(user.getRegionAccount());

            // 计算HashCode
            Date oldExpectedOrderDueDate = null;
            String oldCaseType = null;
            OrderDetailInfo oldOrder = null;
            String oldLabTo = null;
            Integer oldOperationMode = null;
            boolean newOrder = false;
            boolean autoUnbindFlag = false;
            boolean delayChangeFlag = false;
            if (Func.isEmpty(header) || Func.isEmpty(header.getOrderId())) {
                newOrder = true;
            }
            if (header != null && StringUtils.isNotBlank(header.getOrderId())) {
                DatabaseContextHolder.setDatabaseType(DatabaseTypeEnum.Write);
                oldOrder = orderDetailMapper.getOrderDetailInfo(header.getOrderId());
                header.setSetDueDateFlag(oldSlOder.getSetDueDateFlag());
                oldCaseType = oldSlOder.getCaseType();
                oldExpectedOrderDueDate = oldOrder.getExpectedOrderDueDate();
                oldOperationMode = oldOrder.getOperationMode();
                //
                this.setTestRequestContactsInfo(oldOrder.getContacts(), oldOrder.getTestRequest());
                if (oldOrder == null) {
                    rspResult.setMsg("未找到对应的OrderId信息.");
                    return rspResult;
                }
                // 查询order下是否有配置的tag
                ObjectTagInfoExample example = new ObjectTagInfoExample();
                ObjectTagInfoExample.Criteria criteria = example.createCriteria();
                criteria.andObjectIdEqualTo(header.getOrderId());
                List<ObjectTagInfoPO> tagInfoPOList = objectTagInfoMapper.selectByExample(example);
                if (Func.isNotEmpty(tagInfoPOList)) {
                    ObjectTagInfoPO tagInfo = tagInfoPOList.get(0);
                    List<TagValueSelectDTO> savedTags = JSONObject.parseObject(tagInfo.getData().toString(), new TypeReference<List<TagValueSelectDTO>>() {
                    });
                    if (Func.isNotEmpty(savedTags)) {
                        oldOrder.setTagValues(savedTags);
                    }
                }
                reqParams.setHashs(HashUtils.hashCode(oldOrder));
                header.setOrderStatus(oldOrder.getOrderStatus());
                OperationModeEnum operationMode = OperationModeEnum.findMode(header.getOperationMode(), OperationModeEnum.FullCycle);
                switch (operationMode) {
                    case ChargeOnly:
                        reqParams.setOrderType(OrderType.ChargeOnly);
                        break;
                    default:
                        reqParams.setOrderType(StringUtils.isBlank(header.getOrderId()) ? OrderType.NewOrder : OrderType.UpdateOrder);
                        break;
                }
                reqObject.setOperationMode(operationMode.getOperationMode());

                //如果toCp,查询customer信息，校验supplier和mamanufacture是否必填
                if (Func.equalsSafe(header.getToDMFlag(), 1)) {
                    // Customer PK
                    CustomerWeightReq customerWeightReq = new CustomerWeightReq();
                    customerWeightReq.setOrderNo(header.getOrderNo());
                    customerWeightReq.setAction(CustomerWeightType.TrfTemplate.getCode());
                    customerWeightReq.setSystemId(StringUtils.EMPTY);
                    CustomerBuyerGroupInfo customerWeightInfo = customerService.getCustomerWeightInfo(customerWeightReq).getData();
                    if (Func.isNotEmpty(customerWeightInfo)) {
                        CustomerDetailReq customerDetailReq = new CustomerDetailReq();
                        customerDetailReq.setCustomerId(customerWeightInfo.getCustomerId());
                        customerDetailReq.setLocationCode(userLabBu.getLocationCode());
                        customerDetailReq.setBuCode(userLabBu.getProductLineCode());
                        CustomerExtNewListRsp customerExtNewListRsp = customerClient.getCustomerExtInfoByBu(customerDetailReq);
                        if (Func.isNotEmpty(customerExtNewListRsp)) {
                            String needSupplier = customerExtNewListRsp.getNeedSupplier();
                            String needManufacture = customerExtNewListRsp.getNeedManufacture();
                            if (Func.equalsSafe(needSupplier, "1")) {
                                CustomerInfo supplier = reqObject.getSupplier();
                                if (Func.isEmpty(supplier) || (Func.isEmpty(supplier.getCustomerNameCN()) && Func.isEmpty(supplier.getCustomerNameEN()) && Func.isEmpty(supplier.getCustomerAddressCN()) && Func.isEmpty(supplier.getCustomerAddressEN()))) {
                                    rspResult.setMsg("Please Select Supplier!");
                                    rspResult.setSuccess(false);
                                    return rspResult;
                                } else if (Func.isEmpty(supplier.getBossNumber())) {
                                    rspResult.setMsg("Supplier must be a Boss customer!");
                                    rspResult.setSuccess(false);
                                    return rspResult;
                                }
                            }
                            if (Func.equalsSafe(needManufacture, "1")) {
                                CustomerInfo manufacture = reqObject.getManufacture();
                                if (Func.isEmpty(manufacture) || (Func.isEmpty(manufacture.getCustomerNameCN()) && Func.isEmpty(manufacture.getCustomerNameEN()) && Func.isEmpty(manufacture.getCustomerAddressCN()) && Func.isEmpty(manufacture.getCustomerAddressEN()))) {
                                    rspResult.setMsg("Please Select Manufacture!");
                                    rspResult.setSuccess(false);
                                    return rspResult;
                                } else if (Func.isEmpty(manufacture.getBossNumber())) {
                                    rspResult.setMsg("Manufacture must be a Boss customer!");
                                    rspResult.setSuccess(false);
                                    return rspResult;
                                }

                            }
                        }
                    }

                }
                List<CrossLabRelPO> crossLabRelPOS = crossLabService.getToLabByOrder(header.getOrderNo());
                if (CollectionUtils.isNotEmpty(crossLabRelPOS)) {
                    oldLabTo = crossLabRelPOS.get(0).getToLab();
                }
                //自动解绑判断
                if(Func.isNotEmpty(oldOrder.getToDMFlag()) && oldOrder.getToDMFlag().equals(1) && header.getToDMFlag().equals(0)){
                    autoUnbindFlag = true;
                }
                if(Func.isNotEmpty(oldSlOder) && (!Func.equalsSafe(oldSlOder.getDelayType(),header.getDelayType()) || !Func.equalsSafe(oldSlOder.getDelayReason(),header.getDelayReason()))){
                    delayChangeFlag = true;
                }


            }
            // 根据订单操作类型执行不同的Service
            List<Integer> serviceList = this.getAllService();
            OrderType orderType;
            String orderTypeStr = reqObject.getOrderType();
            if (Func.equals(OrderType.CoverPageOrder.getMessage(), orderTypeStr)) {
                orderType = OrderType.CoverPageOrder;
                reqParams.setOrderId(header.getOrderId());
                reqParams.setOrderNo(header.getOrderNo());
                reqParams.setNewOrder(false);
            } else if (Func.equals(OrderType.ENQUIRY.getMessage(), orderTypeStr)) {
                orderType = OrderType.ENQUIRY;
            } else if (Func.equals(OperationModeEnum.ChargeOnly.getOperationMode(), header.getOperationMode())) {
                if (Func.isNotEmpty(header.getOrderId())) {
                    reqParams.setNewOrder(false);
                }
                orderType = OrderType.ChargeOnly;
                //查询配置service
                BuObjectTemplateDTO buObjectTemplateDTO = new BuObjectTemplateDTO();
                buObjectTemplateDTO.setObject("orderV2");
                buObjectTemplateDTO.setOrderModel("ChargeOrder");
                buObjectTemplateDTO.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                serviceList = buSettingService.getSettingServiceList(buObjectTemplateDTO);
            } else {
                orderType = StringUtils.isBlank(header.getOrderId()) ? OrderType.NewOrder : OrderType.UpdateOrder;
            }
            reqParams.setOrderType(orderType);
            reqParams.setServiceList(serviceList);
            // 如果保存订单的时候有手动增加的tag,并且tag支持自动保存的话，需要自动保存
            //校验Tag 必填
            List<TagValueInfoPO> newTagValues = Lists.newArrayList();
            if (Func.equals(orderType, OrderType.ENQUIRY)) {
                reqObject.getHeaders().setTagValueJson(header.getTagValueJson());
            } else {
                reqObject.getHeaders().setTagValueJson(this.saveTagValue(header.getTagValueJson(), header.getBuCode(), TagObjectType.Order.getValue(), newTagValues));
            }
            List<TagValueSelectDTO> tags = new ArrayList<>();
            if (Func.isNotEmpty(header.getTagValueJson())) {
                tags = JSONObject.parseObject(header.getTagValueJson(), new TypeReference<List<TagValueSelectDTO>>() {
                });
            }
            //统一校验各个对象的DueDate
//            BaseResponse dueDateCompareRes = expectDueDateService.compareDueDate(reqObject.getHeaders(), reqObject.getLab(),
//                    isCalculateDueDate(oldOrder, oldSlOder, header), dueDateChange(oldOrder, header));
//
//            if (!Func.equalsSafe(dueDateCompareRes.getStatus(), ResponseCode.SUCCESS.getCode())) {
//                rspResult.setMsg(dueDateCompareRes.getMessage());
//                rspResult.setSuccess(false);
//                return rspResult;
//            }
            // 根据配置校验 subReport reviewer
            if (Func.isNotEmpty(reqObject.getSubcontractFrom()) && Func.isNotEmpty(reqObject.getSubcontractFrom().getCustomerId())) {
                String subcontractLab = reqObject.getSubcontractFrom().getCustomerId();
                String[] subcontractLabArray = subcontractLab.split(" ");
                if (subcontractLabArray.length == 2) {
                    if (Func.isEmpty(reqObject.getSubReportReviewerName()) || Func.isEmpty(reqObject.getSubReportReviewerName().getRegionAccount())) {
                        if (checkWorkFlow(reqObject.getHeaders().getBuCode(), reqObject.getHeaders().getLocationCode(), subcontractLabArray[1])) {
                            rspResult.setMsg("SubReport Reviewer can not be null");
                            rspResult.setSuccess(false);
                            return rspResult;
                        }
                    }
                }
            }
            BuObjectTemplateAllDTO buObjects = null;
            if (OrderType.check(orderType.getType(), OrderType.ENQUIRY)) {
                buObjects = reqObject.getBuObjectTemplate();
            } else {
                if (Func.isNotEmpty(oldOrder)) {
                    // 查询Object Setting中配置的必填字段
                    BuObjectTemplateDTO buObjectTemplateDTO = new BuObjectTemplateDTO();
                    buObjectTemplateDTO.setObject("order");
                    buObjectTemplateDTO.setProductLineCode(oldOrder.getBuCode());
                    buObjectTemplateDTO.setCaseType(oldCaseType);
                    buObjectTemplateDTO.setOrderStatus(oldOrder.getOrderStatus());
                    if(Func.isNotEmpty(oldOrder.getTestRequest())){
                        buObjectTemplateDTO.setReportRequirement(oldOrder.getTestRequest().getReportRequirement());
                    }
                    buObjectTemplateDTO.setOrderModel(oldOrder.getOperationMode() + "");
                    if (Func.equals(OperationModeEnum.ChargeOnly.getOperationMode(), oldOrder.getOperationMode())) {
                        buObjectTemplateDTO.setObject("orderV2");
                        buObjectTemplateDTO.setOrderModel("ChargeOrder");
                        buObjects = buSettingService.getTemplateSettingListV2(buObjectTemplateDTO);
                    } else {
                        buObjects = buSettingService.getTemplateSettingList(buObjectTemplateDTO);
                    }
                } else {
                    String buCode = ProductLineContextHolder.getProductLineCode();
                    BuObjectTemplateDTO buObjectTemplateDTO = new BuObjectTemplateDTO();
                    buObjectTemplateDTO.setObject("order");
                    buObjectTemplateDTO.setProductLineCode(buCode);
                    buObjects = buSettingService.getTemplateSettingList(buObjectTemplateDTO);
                }
            }
            // 单一报告兼容
            //增加一个SampleNo用于排序
            if (Func.isNoneEmpty(reqObject.getReports(),reqObject.getTestMatrixList())) {
                Map<String, OrderMatrixDto> orderMatrixMap  = reqObject.getTestMatrixList().parallelStream()
                        .filter(m->Func.isNotEmpty(m.getExternalMatrixInstanceId()))
                        .collect(Collectors.toMap(OrderMatrixDto::getExternalMatrixInstanceId, Function.identity()));
                List<TestLineInfoDto> testLineInfoDtoList = reqObject.getTestLines();
                if(Func.isNull(testLineInfoDtoList)){
                    testLineInfoDtoList = new ArrayList<>();
                }
                List<ProductSampleRsp> productSampleRspList = reqObject.getProductSampleRspList();
                List<ProductSampleInfo> productSampleList = new ArrayList<>();
                if(Func.isNotEmpty(productSampleRspList)){
                    productSampleList = productSampleRspList.get(0).getProductSamples();
                }
                if(Func.isNull(productSampleList)){
                    productSampleList = new ArrayList<>();
                }
                Map<String, TestLineInfoDto> externalTestlineInstanceMap = testLineInfoDtoList.parallelStream()
                        .filter(tl -> Func.isNotEmpty(tl.getExternalTestlineInstanceId()))
                        .collect(Collectors.toMap(TestLineInfoDto::getExternalTestlineInstanceId, Function.identity()));
                //外部tsi map: external sample id 作为key
                Map<String, ProductSampleInfo> testSampleMap = productSampleList.parallelStream()
                        .filter(s->Func.isNotEmpty(s.getExternalSampleId()))
                        .collect(Collectors.toMap(ProductSampleInfo::getExternalSampleId, Function.identity()));
                List<ReportInfo> reports = reqObject.getReports();
                for (ReportInfo reportInfo : reports) {
                    if(Func.isEmpty(reportInfo.getReportMatrix())){
                        continue;
                    }
                    Set<String> sampleNos = Sets.newHashSet();
                    ArrayListMultimap<String, ProductSampleInfo> tliSampleMap = ArrayListMultimap.create();
                    for (OrderReportMatrixDto reportMatrix : reportInfo.getReportMatrix()) {
                        logger.info("register report reportMatrix:{}",JSON.toJSONString(reportMatrix));
                        OrderMatrixDto orderMatrixDto = orderMatrixMap.get(reportMatrix.getExternalMatrixInstanceId());
                        if(Func.isEmpty(orderMatrixDto)){
                            continue;
                        }
                        TestLineInfoDto testLineInfoDto = externalTestlineInstanceMap.get(orderMatrixDto.getExternalTestlineInstanceId());
                        if(Func.isEmpty(testLineInfoDto)){
                            continue;
                        }
                        ProductSampleInfo productSampleInfo = testSampleMap.get(orderMatrixDto.getExternalSampleInstanceId());
                        if(Func.isEmpty(productSampleInfo)){
                            continue;
                        }
                        tliSampleMap.put(testLineInfoDto.getExternalTestlineInstanceId(),productSampleInfo);
                        sampleNos.add(productSampleInfo.getSampleID());
                    }
                    if(Func.isNotEmpty(sampleNos)){
                        reportInfo.setSortBy(sampleNos.stream().sorted().collect(Collectors.joining("")));
                    }
                }
                List<Integer> refSystemIdList = new ArrayList<>();
                if (Func.isNotEmpty(reqObject.getHeaders().getReferences())) {
                    refSystemIdList = ReferenceConvertor.toRefSystemIdList(reqObject.getHeaders().getReferences()).stream().collect(Collectors.toList());
                }
                if(Func.isNotEmpty(refSystemIdList) &&
                        !refSystemIdList.contains(RefSystemIdEnum.DAL.getRefSystemId())
                ){
                    reports = reports.stream()
                            .sorted(Comparator.nullsLast(Comparator.comparing(ReportInfo::getSortBy)))
                            .collect(Collectors.toList());
                }
                ReportInfo reportInfo = reports.get(0);
                reqObject.setReport(reportInfo);
                reqObject.setReports(reports);
            }
            if (Func.isNotEmpty(buObjects)) {
                String caseType = header.getCaseType();
                if (Func.equals(caseType, "IDB") || Func.equals(caseType, "IDN")
                        || Func.equals(caseType, "IDN TJ")) {
                    Map<String, String> requiredFields = Maps.newHashMap();
                    this.convertToMap(buObjects.getObjectSettingList(), requiredFields);
                    if (Func.isNotEmpty(requiredFields) && requiredFields.containsKey(Constants.BU_OBJECT.REFERENCE_ORDER_NO)) {
                        String subcontractOrderNo = "";
                        if (Func.isNotEmpty(references)) {
                            ReferenceInfo subcontractReferenceInfo = references.stream().filter(i -> RefSystemIdEnum.check(i.getRefSystemId(), RefSystemIdEnum.SubContract)).findFirst().orElse(null);
                            if (Func.isNotEmpty(subcontractReferenceInfo)) {
                                subcontractOrderNo = subcontractReferenceInfo.getExternalOrderNo();
                            }
                        }
                        if (Func.isEmpty(subcontractOrderNo)) {
                            rspResult.setMsg("Subcontract Order No 不能为空。");
                            rspResult.setSuccess(false);
                            return rspResult;
                        }
                    }
                }

            }
            BuObjectTemplateAllDTO finalBuObjects = buObjects;
            OrderDetailInfo finalOldOrder = oldOrder;
            SlOrderInfoPO finalOldSlOder = oldSlOder;
            CustomResult finalRspResult = rspResult;
            boolean finalNewOrder = newOrder;
            boolean finalAutoUnbindFlag = autoUnbindFlag;
            boolean finaldelayChangeFlag = delayChangeFlag;
            rspResult = transactionTemplate.execute(trans -> {
                reqObject.getHeaders().setModifitedDate(DateUtils.getNow());
                CustomResult saveResult = orderDetailImpl.doInvoke(reqObject, reqParams);
                if (saveResult.isSuccess()) {
                    // 订单保存成功之后计算DueDate
                    BaseResponse calculateRes = expectDueDateService.updateExpectDueDate(reqObject.getHeaders(), reqObject.getLab(),
                            isCalculateDueDate(finalOldOrder, finalOldSlOder, header), dueDateChange(finalOldOrder, header));
                    if (!Func.equalsSafe(calculateRes.getStatus(), ResponseCode.SUCCESS.getCode())) {
                        trans.setRollbackOnly();
                        saveResult.setMsg(calculateRes.getMessage());
                        saveResult.setSuccess(false);
                        return saveResult;
                    }
                    // Order Confirm之后需要校验订单的必填字段
                    if (Func.isNotEmpty(finalOldOrder) && OrderStatus.checkStatus(finalOldOrder.getOrderStatus(), OrderStatus.Confirmed, OrderStatus.Testing,
                        OrderStatus.Reporting, OrderStatus.Completed, OrderStatus.Closed)) {
                        // 基于ObjectSetting的必填项校验
                        CheckRequiredFieldReq checkReq = new CheckRequiredFieldReq();
                        checkReq.setObject("order");
                        checkReq.setObjectId(header.getOrderId());
                        checkReq.setProductLineCode(finalOldOrder.getBuCode());
                        checkReq.setToken(token);
                        checkReq.setOperationMode(finalOldOrder.getOperationMode());
                        checkReq.setBuObjects(finalBuObjects);
                        checkReq.setCaseType(header.getCaseType());
                        try {
                            CustomResult<CheckRequiredFieldRsp> checkRequiredResult = checkRequiredField(checkReq);
                            if (Func.isNotEmpty(checkRequiredResult.getData()) && Func.isNotEmpty(checkRequiredResult.getData().getErrorMessage())) {
                                trans.setRollbackOnly();
                                saveResult.setMsg(checkRequiredResult.getMsg());
                                saveResult.setSuccess(false);
                                return saveResult;
                            }
                            CustomResult<OrderDetailInfo> confirmOrderCheckRes = this.checkBaseField(finalOldOrder.getOrderId());
                            if (!Func.equalsSafe(confirmOrderCheckRes.getStatus(), ResponseCode.SUCCESS.getCode())) {
                                trans.setRollbackOnly();
                                saveResult.setMsg(confirmOrderCheckRes.getMsg());
                                saveResult.setSuccess(false);
                                return saveResult;
                            }
                        } catch (IllegalAccessException e) {
                            logger.info("SaveOrderRequiredCheck e:{}", e.getMessage());
                        }
                    }
                    if(finalAutoUnbindFlag){
                        //查询Trf
                        List<OrderTrfRelInfo>  trfList = orderTrfRelationshipMapper.getTrfRelationshipByOrderId(reqParams.getOrderId());
                        OrderIdReq orderIdReq = new OrderIdReq();
                        orderIdReq.setOrderId(reqParams.getOrderId());
                        if(Func.isNotEmpty(trfList)){
                            List<OrderTrfRelInfo> unbindTrfList = trfList.stream().filter(e -> RefIntegrationChannel.check(e.getIntegrationChannel(),RefIntegrationChannel.SCI) && Func.equalsSafe(TrfSourceType.Order2TRF.getSourceType(),e.getTrfSourceType())).collect(Collectors.toList());
                            if(Func.isNotEmpty(unbindTrfList)){
                                //Order与Trf解绑 同步调用SCI接口
                                EventType eventType = EventType.findEventType(EventType.Unbound.getTypeName());
                                GpoSciTrfSyncReq gpoSciTrfSyncReq = new GpoSciTrfSyncReq();
                                gpoSciTrfSyncReq.setOrderId(reqParams.getOrderId());
                                gpoSciTrfSyncReq.setOrderNo(reqParams.getOrderNo());
                                gpoSciTrfSyncReq.setObjectType(StandardObjectType.Order.getName());
                                gpoSciTrfSyncReq.setSourceId(reqParams.getOrderId());
                                gpoSciTrfSyncReq.setSourceNo(reqParams.getOrderNo());
                                gpoSciTrfSyncReq.setEventType(EventType.Unbound.getTypeName());
                                gpoSciTrfSyncReq.setSciAction(eventType.getSciAction());
                                gpoSciTrfSyncReq.setRefSystemId(unbindTrfList.get(0).getRefSystemId());
                                gpoSciTrfSyncReq.setToken(token);
                                gpoSciTrfSyncReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                                gpoSciTrfSyncReq.setTrfNo(unbindTrfList.get(0).getRefNo());
                                BaseResponse<Boolean> unbindRsp = sciFacade.unBindSciTrf(gpoSciTrfSyncReq);
                                //logger.info("调用unbind,Result:{}",JSON.toJSONString(unbindRsp));
                                if(unbindRsp.isSuccess()){
                                    orderTrfRelationshipMapper.deleteByRefNos(reqParams.getOrderId(), unbindTrfList.stream().map(e -> e.getRefNo()).collect(Collectors.toSet()));
                                    orderMapper.updateSlOrderToNull(reqParams.getOrderId());

                                } else{
                                    trans.setRollbackOnly();
                                    saveResult.setMsg(unbindRsp.getMessage());
                                    saveResult.setSuccess(false);
                                    return saveResult;
                                }
                                BizLogInfo bizLog = new BizLogInfo();
                                bizLog.setBu(reqObject.getHeaders().getBuCode());
                                bizLog.setLab(reqObject.getHeaders().getLocationCode());
                                bizLog.setOpUser(SystemContextHolder.getRegionAccount());
                                bizLog.setBizId(reqParams.getOrderNo());
                                bizLog.setOpType("Unbind TRF");
                                bizLog.setBizOpType(BizLogConstant.ORDER_OPERATION_HISTORY);
                                bizLog.setNewVal("Unbind TRFNo." + unbindTrfList.get(0).getRefNo());
                                bizLogClient.doSend(bizLog);
                            }
                        }
                    }
                    if(finaldelayChangeFlag){
                        BizLogInfo bizLog = new BizLogInfo();
                        bizLog.setBu(reqObject.getHeaders().getBuCode());
                        bizLog.setLab(reqObject.getHeaders().getLocationCode());
                        bizLog.setOpUser(SystemContextHolder.getRegionAccount());
                        bizLog.setBizId(reqParams.getOrderNo());
                        bizLog.setOpType("Order Delay");
                        bizLog.setBizOpType(BizLogConstant.ORDER_OPERATION_HISTORY);
                        bizLog.setNewVal("Delay type:" + header.getDelayTypeValue() + ",Delay Reson:" + header.getDelayReason());
                        bizLogClient.doSend(bizLog);
                    }
                    // 事务提交后再发送消息出去，防止消息被提前消费
                    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            if (finalRspResult.isSuccess()) {
                                if (finalNewOrder) {
                                    PreEvent preEvent = new PreEvent();
                                    preEvent.setEventSource(StandardObjectType.Order.getName());
                                    preEvent.setEventType(EventType.New.getTypeName());
                                    preEvent.setToken(token);
                                    preEvent.setEventSourceStatus(OrderStatus.New.getStatus());
                                    preEvent.setEventSourceNo(reqParams.getOrderNo());
                                    preEvent.setEventSourceId(reqParams.getOrderId());
                                    preEvent.setOrderId(reqParams.getOrderId());
                                    preEvent.setOrderNo(reqParams.getOrderNo());
                                    preEvent.setProductLineCode(userLabBu.getProductLineCode());
//                                  eventCenterService.processEvent(preEvent);
                                    scheduleTaskClient.sendEventMessage(preEvent, userLabBu.getLabCode());
                                }
                            }
                        }
                    });

                } else {
                    trans.setRollbackOnly();
                }
                return saveResult;
            });
            logger.info("OrderService.saveOrder{},Result:{}",reqParams.getOrderNo(),JSON.toJSONString(rspResult));

            if (rspResult.isSuccess()) {
                List<TagValueSelectDTO> finalTags = tags;
                taskExecutor.execute(() -> {
                    //1、保存新Tag Value
                    if (CollectionUtils.isNotEmpty(newTagValues)) {
                        tagValueExtMapper.insertBatch(newTagValues);
                    }
                    //2、tag value 如果删除再开启的情况,启用原先删除的数据；
                    List<String> tagValueIds = new ArrayList<>();
                    finalTags.forEach(tag -> {
                        if (Func.isNotEmpty(tag.getSelectedValue())) {
                            tagValueIds.addAll(tag.getSelectedValue());
                        }
                    });
                    if (Func.isNotEmpty(tagValueIds)) {
                        tagValueExtMapper.enableBatch(tagValueIds);
                    }
                    // DAL 建单时需要保存操作记录
                    if(finalNewOrder && Func.isNotEmpty(reqObject.getHeaders().getReferences())){
                        ReferenceInfo dalRef = reqObject.getHeaders().getReferences().stream().filter(item->
                                RefSystemIdEnum.check(item.getRefSystemId(),RefSystemIdEnum.DAL,RefSystemIdEnum.DML,RefSystemIdEnum.RSTS)).findAny().orElse(null);
                        if(Func.isNotEmpty(dalRef)){
                            // 走本地更新ToDo状态
                            if(header.getCompleteTodo()){
                                ToDoListInfoPO todoStatusReq = new ToDoListInfoPO();
                                todoStatusReq.setStatus(TodoStatus.Completed.getCode());
                                todoStatusReq.setObject_no(dalRef.getReferenceNo());
                                todoStatusReq.setObject_id(dalRef.getExtObjectId());
                                if(RefSystemIdEnum.check(dalRef.getRefSystemId(),RefSystemIdEnum.RSTS)) {
                                    todoStatusReq.setObject_no(dalRef.getExternalOrderNo());
                                }
                                todoStatusReq.setTo_lab_code(header.getLabCode());
                                todoStatusReq.setModified_by(user.getRegionAccount());
                                todoStatusReq.setModified_date(new Date());
                                int count = gpoToDoListExtMapper.updateStatus(todoStatusReq);
                            }
                            OperationRemarkDTO operationRemarkDTO = new OperationRemarkDTO();
                            if(Func.isNotEmpty(dalRef.getExtData())){
                                JSONObject extRefJson = JSON.parseObject(dalRef.getExtData());
                                if(Func.isNotEmpty(extRefJson.get("todoId"))){
                                    operationRemarkDTO.setId(extRefJson.get("todoId").toString());
                                }
                            }
                            operationRemarkDTO.setOrderNo(dalRef.getReferenceNo());
                            operationRemarkDTO.setRemark(OperationTypeEnums.ToDoCreateOrder.getValue());
                            operationRemarkDTO.setToken(token);
                            iOperationHistoryService.orderOperationHistorySave(operationRemarkDTO,OperationTypeEnums.ToDoCreateOrder.getValue());
                    }}
                });
                if (!Func.equals(OperationModeEnum.ChargeOnly.getOperationMode(), reqObject.getHeaders().getOperationMode())) {
                    //报告模板检查，不符合语言时删除模板
                    ReportTemplateCheckReq reportTemplateCheckReq = new ReportTemplateCheckReq();
                    reportTemplateCheckReq.setLanguageId(Integer.valueOf(reqObject.getTestRequest().getReportLanguage()));
                    reportTemplateCheckReq.setOrderNo(reqObject.getHeaders().getOrderNo());
                    reportTemplateCheckReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                    BaseResponse baseResponse = reportFacade.reportTemplateCheck(reportTemplateCheckReq);
                    if (Func.isEmpty(baseResponse)) {
                        throw new Exception("根据报告语言检查报告模板程序失败");
                    }
                }

                OrderDetailRsp rspDetail = new OrderDetailRsp();
                rspDetail.setOrderId(reqParams.getOrderId());
                rspDetail.setOrderNo(reqParams.getOrderNo());
                rspResult.setData(rspDetail);
                //更新enableDelivery
                this.updateEnableDeliveryByOrderId(reqParams.getOrderId());
                // 发送Kafka
                kafkaService.sendMessage(KafkaActionType.orderCreation, header, token, user.getRegionAccount(), reqParams);

                // POSL-3924
                saveRefBossOrder(reqParams, header, user, oldSlOder, newBossOrderNo, newRefBossInvoiceNo);

                //是否第一次维护parcel
                if (newParcelNo) {
                    int dbParcelCount = parcelMapper.getCountByOrderId(reqParams.getOrderId());
                    if(dbParcelCount > 0) {
                        // 查询订单对应的最早的Parcel Receive Date
                        trackingService.sendParcelReceived(header.getOrderNo(), token, user.getRegionAccount());
                    }
//                    ParcelInfoPO parcelInfo = parcelMapper.getOrderParcelReceiveTime(header.getOrderId());
//                    if(Func.isNotEmpty(parcelInfo)&&Func.isNotEmpty(parcelInfo.getParcelReceiveTime())){
//                        header.setParcelReceiveDate(parcelInfo.getParcelReceiveTime());
//                    }
//                    kafkaService.sendTracking(KafkaActionType.parcelReceived, header, token, user.getRegionAccount(), StringUtils.join(header.getParcelNo(), ";"));
                }
                if (header.getToTestFlag() != null && header.getToTestFlag() == 1) {
                    OrderPersonInfo tsPerson = reqObject.getTsPerson();
                    gpnUpdateOrder(header.getOrderNo(), header.getOrderId(), header.getResponsibleTeamCode(), header.getcSName(), user.getRegionAccount(),
                            (tsPerson != null && Func.isNotEmpty(tsPerson)) ? tsPerson.getRegionAccount() : null);
                }
                //查询订单信息
                OrderInfoPO orderInfoPO = orderMapper.getInfoByOrderNo(reqObject.getHeaders().getOrderNo());
                if (!Func.equalsSafe(oldCaseType, reqObject.getHeaders().getCaseType())) {
                    BizLogInfo bizLog = new BizLogInfo();
                    bizLog.setBizId(reqObject.getHeaders().getOrderNo());
                    bizLog.setBu((Func.isNotEmpty(orderInfoPO) && Func.isNotEmpty(orderInfoPO.getBUCode())) ? orderInfoPO.getBUCode() : user.getCurrentLabCode().split(" ")[1]);
                    bizLog.setLab((Func.isNotEmpty(orderInfoPO) && Func.isNotEmpty(orderInfoPO.getLocationCode())) ? orderInfoPO.getLocationCode() : user.getCurrentLabCode().split(" ")[0]);
                    bizLog.setBizOpType(BizLogConstant.ORDER_OPERATION_HISTORY);
                    bizLog.setOpType("Order CaseType Change");
                    bizLog.setOpUser(user.getRegionAccount());
                    bizLog.setOriginalVal(oldCaseType);
                    bizLog.setNewVal(reqObject.getHeaders().getCaseType());
                    bizLogClient.doSend(bizLog);
                }
                if (Func.isNotEmpty(oldLabTo) || Func.isNotEmpty(header.getShareToLab())) {
                    if (!Func.equalsSafe(oldLabTo, header.getShareToLab())) {
                        BizLogInfo bizLog = new BizLogInfo();
                        bizLog.setBizId(reqObject.getHeaders().getOrderNo());
                        bizLog.setBu((Func.isNotEmpty(orderInfoPO) && Func.isNotEmpty(orderInfoPO.getBUCode())) ? orderInfoPO.getBUCode() : header.getBuCode());
                        bizLog.setLab((Func.isNotEmpty(orderInfoPO) && Func.isNotEmpty(orderInfoPO.getLocationCode())) ? orderInfoPO.getLocationCode() : header.getLocationCode());
                        bizLog.setBizOpType(BizLogConstant.ORDER_OPERATION_HISTORY);
                        bizLog.setOpType("Order Tops Change");
                        bizLog.setOpUser(user.getRegionAccount());
                        bizLog.setOriginalVal(oldLabTo);
                        bizLog.setNewVal(header.getShareToLab());
                        bizLogClient.doSend(bizLog);
                    }
                }
                // 订单保存成功之后计算DueDate
//                BaseResponse calculateRes = expectDueDateService.updateExpectDueDate(reqObject.getHeaders(), reqObject.getLab(),
//                        isCalculateDueDate(oldOrder,oldSlOder,header),dueDateChange(oldOrder,header));
//                if(!Func.equalsSafe(calculateRes.getStatus(),ResponseCode.SUCCESS.getCode())){
//                    rspResult.setMsg(calculateRes.getMessage());
//                }
                // CoverPageOrder 特殊处理不再触发SL_Order更新
                if (Func.equalsSafe(orderType.getType(), OrderType.CoverPageOrder.getType())) {
                    SlOrderInfoPO slOrderInfo = new SlOrderInfoPO();
                    slOrderInfo.setSubcontractFee(reqObject.getHeaders().getSubcontractFee());
                    slOrderInfo.setSubcontractFeeCurrency(reqObject.getHeaders().getSubcontractFeeCurrency());
                    slOrderInfo.setOrderId(reqObject.getHeaders().getOrderId());
                    updateSubcontractFee(slOrderInfo);
                }
                try {
                    if (reqObject.isNeedUpdateLabSection()) {
                        UpdateLabSectionReq updateLabSectionReq = new UpdateLabSectionReq();
                        updateLabSectionReq.setOrderNo(reqObject.getHeaders().getOrderNo());
                        updateLabSectionReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                        updateLabSectionReq.setToken(token);
                        updateLabSectionReq.setAssignAllLabSection(reqObject.isAssignAllLabSection());
                        testLineFacade.updateLabSectionByOrder(updateLabSectionReq);
                    }
                } catch (Exception e) {
                    logger.error("tops change update LabSection error:{}", e);
                }
                checkAndHandleOrderQualificationTypeChange(oldOrder, reqObject, finalBuObjects);
            }
            OperationRemarkDTO orderDueDateChangeOperation = reqObject.getOrderDueDateChangeOperation();
            try {
                OrderDetailInfo orderDetailInfo = orderDetailMapper.getOrderDetailInfo(header.getOrderId());
                if ((oldExpectedOrderDueDate == null && reqObject.getHeaders().getExpectedOrderDueDate() != null) || (oldExpectedOrderDueDate != null && reqObject.getHeaders().getExpectedOrderDueDate() == null) || !DateUtils.format(oldExpectedOrderDueDate).equalsIgnoreCase(DateUtils.format(reqObject.getHeaders().getExpectedOrderDueDate()))) {
                    BizLogInfo bizLog = new BizLogInfo();
                    bizLog.setBizId(orderDetailInfo.getOrderNo());
                    bizLog.setBu((Func.isNotEmpty(orderDetailInfo) && Func.isNotEmpty(orderDetailInfo.getBuCode())) ? orderDetailInfo.getBuCode() : user.getCurrentLabCode().split(" ")[1]);
                    bizLog.setLab((Func.isNotEmpty(orderDetailInfo) && Func.isNotEmpty(orderDetailInfo.getLocationCode())) ? orderDetailInfo.getLocationCode() : user.getCurrentLabCode().split(" ")[0]);
                    bizLog.setBizOpType(BizLogConstant.ORDER_OPERATION_HISTORY);
                    bizLog.setOpType("Order expect due date");
                    bizLog.setOpUser(user.getRegionAccount());
                    bizLog.setOriginalVal(DateUtils.format(oldExpectedOrderDueDate));
                    String bizlogNewVal = "";
                    bizlogNewVal += Func.isNotEmpty(orderDetailInfo.getExpectedOrderDueDate())
                            ? DateFormatUtil.formatDate("yyyy-MM-dd HH:mm:ss", orderDetailInfo.getExpectedOrderDueDate()) : "";
                    if (Func.isNotEmpty(orderDueDateChangeOperation)) {
                        if (Func.isNotEmpty(orderDueDateChangeOperation.getReasonType()) || Func.isNotEmpty(orderDueDateChangeOperation.getRemark())) {
                            if (Func.isNotEmpty(orderDueDateChangeOperation.getReasonType())) {
                                bizlogNewVal += " Reason Type：";
                                bizlogNewVal += orderDueDateChangeOperation.getReasonType();
                            }
                            bizlogNewVal += " Reason Remark：";
                            bizlogNewVal += Func.toStr(orderDueDateChangeOperation.getRemark());
                        }
                    }
                    bizLog.setNewVal(bizlogNewVal);
                    bizLogClient.doSend(bizLog);
                }
                if ((oldOperationMode == null && reqObject.getHeaders().getOperationMode() != null) || (oldOperationMode != null && reqObject.getHeaders().getOperationMode() == null) || !Func.equalsSafe(oldOperationMode, reqObject.getHeaders().getOperationMode())) {
                    BizLogInfo bizLog = new BizLogInfo();
                    bizLog.setBizId(orderDetailInfo.getOrderNo());
                    bizLog.setBu((Func.isNotEmpty(orderDetailInfo) && Func.isNotEmpty(orderDetailInfo.getBuCode())) ? orderDetailInfo.getBuCode() : user.getCurrentLabCode().split(" ")[1]);
                    bizLog.setLab((Func.isNotEmpty(orderDetailInfo) && Func.isNotEmpty(orderDetailInfo.getLocationCode())) ? orderDetailInfo.getLocationCode() : user.getCurrentLabCode().split(" ")[0]);
                    bizLog.setBizOpType(BizLogConstant.ORDER_OPERATION_HISTORY);
                    bizLog.setOpType("Order Operation Mode");
                    bizLog.setOpUser(user.getRegionAccount());
                    bizLog.setNewVal("From[" + (Func.toStr(OperationModeEnums.getMessage(oldOperationMode))) + "] To [" + (Func.toStr(OperationModeEnums.getMessage(reqObject.getHeaders().getOperationMode()))) + "]");
                    bizLogClient.doSend(bizLog);
                }
                //两个TrfNo不同，解绑或绑定(新绑定、修改)
//                if(!Func.equals(oldTrfNos,newTrfNos)){
//                    Integer bindStatus = null;
//                    if(!Func.isEmpty(newTrfNos)){//绑定
//                        bindStatus = TrfBindStatusEnums.BIND_TRF.getStatus();
//                    }
//                    BizLogInfo bizLog = new BizLogInfo();
//                    bizLog.setBu(orderDetailInfo.getBuCode());
//                    bizLog.setLab(orderDetailInfo.getLocationCode());
//                    bizLog.setOpUser(SystemContextHolder.getRegionAccount());
//                    bizLog.setBizId(orderDetailInfo.getOrderNo());
//                    bizLog.setOpType(TrfBindStatusEnums.checkStatus(bindStatus, TrfBindStatusEnums.BIND_TRF) ? "ByTRFCreate" : "Unbind TRF");
//                    bizLog.setBizOpType(BizLogConstant.ORDER_OPERATION_HISTORY);
//                    bizLog.setNewVal(TrfBindStatusEnums.checkStatus(bindStatus, TrfBindStatusEnums.BIND_TRF) ? "Bind TRFNo."+newTrfNos : "Unbind TRFNo."+newTrfNos);
//                    bizLog.setOriginalVal(oldTrfNos);
//                    bizLogClient.doSend(bizLog);
//                }
            } catch (Exception e) {
                logger.error("send bizLog err:{}", e.getMessage());
            }
            try {
                if (Func.isNotEmpty(orderDueDateChangeOperation)) {
                    if (Func.isNotEmpty(orderDueDateChangeOperation.getReasonType()) || Func.isNotEmpty(orderDueDateChangeOperation.getRemark())) {
                        OperationRemarkDTO operationRemarkDTO = new OperationRemarkDTO();
                        operationRemarkDTO.setId(reqObject.getHeaders().getOrderId());
                        operationRemarkDTO.setOrderNo(reqObject.getHeaders().getOrderNo());
                        operationRemarkDTO.setRemark(orderDueDateChangeOperation.getRemark());
                        operationRemarkDTO.setReasonType(orderDueDateChangeOperation.getReasonType());
                        iOperationHistoryService.orderOperationHistorySave(operationRemarkDTO, OperationTypeEnums.ChangeOrderDueDate.getStatus());
                        // 发送邮件
                        if (Func.isNotEmpty(reqObject.getDueDateChangeList())) {
                            String combinedChangeReason = "";
                            if (Func.isNotEmpty(orderDueDateChangeOperation.getReasonType())) {
                                combinedChangeReason = combinedChangeReason.concat("【").concat(orderDueDateChangeOperation.getReasonType()).concat("】");
                            }
                            if (Func.isNotEmpty(orderDueDateChangeOperation.getRemark())) {
                                combinedChangeReason = combinedChangeReason.concat(",【").concat(orderDueDateChangeOperation.getRemark()).concat("】");
                            }
                            expectDueDateService.sendDueDateChangeEmail(Func.copy(reqObject.getDueDateChangeList(), DueDateChangeReq.class, DueDateChangeDTO.class)
                                    , reqObject, user.getRegionAccount(), combinedChangeReason);
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("save ChangeOrderDueDate OperationHistory error:{}", e);
            }
            OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setOrderId(reqObject.getHeaders().getOrderId());
            String oldCsName = null;
            if (Func.isNotEmpty(oldSlOder) && Func.isNotEmpty(oldSlOder.getcSName())) {
                oldCsName = oldSlOder.getcSName();
            }
            this.sendOrderDueDate(orderIdReq, oldExpectedOrderDueDate, oldCsName, false);
        } catch (Exception ex) {
            logger.error("OrderDetailService.saveOrderInfo 处理订单({})业务异常：{}，堆栈信息：", reqParams.getOrderNo(), reqObject, ex);
            rspResult.setSuccess(false);
            rspResult.setStackTrace(ExceptionUtil.getStackTrace(ex));
            rspResult.setMsg(String.format("处理订单业务异常：%s.", ExceptionUtil.parseErrorMsg(ex.getMessage())));

        } finally {
            SystemLog systemLog = new SystemLog();
            systemLog.setObjectType("order");
            systemLog.setObjectNo(reqObject.getHeaders().getOrderNo());
            systemLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            systemLog.setType(SystemLogType.API.getType());
            systemLog.setRemark("saveOrderInfo");
            systemLog.setCreateBy(regionAccount);
            systemLog.setLocationCode(ProductLineContextHolder.getLocationCode());
            systemLog.setOperationType("saveOrderInfo");
            systemLog.setRequest(reqData);
            systemLog.setResponse(JSON.toJSONString(rspResult));
            systemLogHelper.save(systemLog);

            stopWatch.stop();
            //logger.info("OrderDetailService.saveOrderInfo 订单({})请求接口耗时：{}.", reqParams.getOrderNo(), stopWatch.getTime());
        }

        return rspResult;
    }

    /**
     * 检查并处理订单资格类型变更
     * @param oldOrder 旧订单信息
     * @param reqObject 新订单请求对象
     * @param finalBuObjects 业务对象配置
     */
    private void checkAndHandleOrderQualificationTypeChange(OrderDetailInfo oldOrder,
                                                            OrderDetailDto reqObject,
                                                            BuObjectTemplateAllDTO finalBuObjects
                                                            ) {
        try {
            String oldQualificationType = Optional.ofNullable(oldOrder)
                    .map(OrderDetailInfo::getTestRequest)
                    .map(TestRequestInfo::getQualificationType)
                    .map(Func::toStr)
                    .orElse("");

            String newQualificationType = Optional.ofNullable(reqObject)
                    .map(OrderDetailDto::getTestRequest)
                    .map(TestRequestInfo::getQualificationType)
                    .map(Func::toStr)
                    .orElse("");
            String token = reqObject.getToken();
            OrderHeaderInfo header = reqObject.getHeaders();
            boolean changeToReport = shouldChangeToReport(finalBuObjects);

            if (changeToReport) {
                handleOrderQualificationTypeChange(oldQualificationType, newQualificationType, token, header);
            }
        } catch (Exception e) {
            logger.error("checkAndHandleOrderQualificationTypeChange error", e);
        }
    }

    /**
     * 判断是否需要变更到报告状态
     * @param finalBuObjects 业务对象配置
     * @return 是否需要变更
     */
    private boolean shouldChangeToReport(BuObjectTemplateAllDTO finalBuObjects) {
        List<BuObjectSettingDTO> objectSettingList = finalBuObjects.getObjectSettingList();
        if (Func.isEmpty(objectSettingList)) {
            return false;
        }

        // 查找服务需求配置
        BuObjectSettingDTO orderSearchBuSetting = objectSettingList.stream()
                .filter(buObjectSettingDTO -> Func.equalsIgnoreCase(
                        buObjectSettingDTO.getAttributeCode(),
                        Constants.BU_OBJECT.SERVICE_REQUIREMENT))
                .findFirst()
                .orElse(null);

        if (Func.isEmpty(orderSearchBuSetting) || Func.isEmpty(orderSearchBuSetting.getSubSettingList())) {
            return false;
        }

        // 查找资格类型配置
        BuObjectSettingDTO accreditationRequestSetting = orderSearchBuSetting.getSubSettingList().stream()
                .filter(buObjectSettingDTO -> Func.equalsIgnoreCase(
                        buObjectSettingDTO.getAttributeCode(),
                        ServiceRequirementSettingEnums.qualificationType.getCode()))
                .findFirst()
                .orElse(null);

        if (Func.isEmpty(accreditationRequestSetting) || Func.isEmpty(accreditationRequestSetting.getFuncValueMap())) {
            return true;
        }

        Map<String, Object> funcValueMap = accreditationRequestSetting.getFuncValueMap();
        return Func.toBoolean(funcValueMap.getOrDefault("changeToReport", true), true);
    }

    private void handleOrderQualificationTypeChange(String oldQualificationType,String newQualificationType,String token,OrderHeaderInfo header){
        //
        if(!Func.equalsSafe(oldQualificationType,newQualificationType)){
            OrderQualificationTypeUpdateReq orderQualificationTypeUpdateReq = new OrderQualificationTypeUpdateReq();
            orderQualificationTypeUpdateReq.setOrderIdList(Sets.newHashSet(header.getOrderId()));
            orderQualificationTypeUpdateReq.setToken(token);
            orderQualificationTypeUpdateReq.setProductLineCode(header.getBuCode());
            BaseResponse baseResponse = orderTempFacade.onOrderQualificationTypeChange(orderQualificationTypeUpdateReq);
            logger.info("onOrderQualificationTypeChange rsp:{}", JSON.toJSONString(baseResponse));
        }
    }

    private boolean checkWorkFlow(String buCode, String locationCode, String subcontractBu) {
        if (Func.isEmpty(buCode) || Func.isEmpty(locationCode) || Func.isEmpty(subcontractBu)) {
            return false;
        }
        BuReportWorkflowDTO buReportWorkflowDTO = frameWorkClient.getBuWorkFlow(buCode, locationCode);
        if (Func.isEmpty(buReportWorkflowDTO)) {
            return false;
        }
        if (Func.isEmpty(buReportWorkflowDTO.getHostReview()) || Func.isEmpty(buReportWorkflowDTO.getHostReview().getFromProductLineCode())) {
            return false;
        }
        String fromProductLineCode = buReportWorkflowDTO.getHostReview().getFromProductLineCode();
        String[] fromProductLineCodes = fromProductLineCode.split(",");
        return Arrays.asList(fromProductLineCodes).contains(subcontractBu);
    }

    private void saveRefBossOrder(SysParamInfo reqParams, OrderHeaderInfo header, UserInfo user, SlOrderInfoPO oldSlOder, String newBossOrderNo, String newRefInvoiceNo) throws Exception {
        oldSlOder = oldSlOder == null ? new SlOrderInfoPO() : oldSlOder;

        boolean isChange = Func.isNotEmpty(newBossOrderNo) && !Func.equalsSafe(Func.toStr(newBossOrderNo), Func.toStr(oldSlOder.getBossOrderNo())) || !Func.equalsSafe(Func.toStr(newRefInvoiceNo), Func.toStr(oldSlOder.getRefBossInvoiceNo()));
        boolean isBossOrderNoChange = Func.isNotEmpty(newBossOrderNo) && !Func.equalsSafe(Func.toStr(newBossOrderNo), Func.toStr(oldSlOder.getBossOrderNo()));
        boolean allowInvoiceConfirmStatus = OrderStatus.checkStatus(header.getOrderStatus(), OrderStatus.New, OrderStatus.Confirmed, OrderStatus.Completed, OrderStatus.Testing, OrderStatus.Reporting, OrderStatus.Pending);
        List<QuotationHeadRsp> quotationHeadRspList = new ArrayList<>();
        if(Func.equalsSafe(header.getSystemId(),SgsSystem.GPO.getSgsSystemId())){
            OrderIdsRequest objOrderIdsRequest = new OrderIdsRequest();
            objOrderIdsRequest.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            objOrderIdsRequest.setOrderIds(Lists.newArrayList(reqParams.getOrderId()));
            objOrderIdsRequest.setSystemId(SgsSystem.GPO.getSgsSystemId());
            BaseResponse<List<QuotationHeadRsp>> quoBaseResponse = quotationFacade.getActivedQuotationByOrderIds(objOrderIdsRequest);
            quotationHeadRspList = quoBaseResponse == null || Func.isEmpty(quoBaseResponse.getData()) ? Lists.newArrayList() : quoBaseResponse.getData();
        }

        if (Func.isNotEmpty(quotationHeadRspList) && quotationHeadRspList.size() == 1) {
            boolean isTriggerInvoiceConfirm = false;
            if (isBossOrderNoChange && allowInvoiceConfirmStatus) {
/*                ToBossReq objToBossReq=new ToBossReq();
                objToBossReq.setOrderIds(Lists.newArrayList(reqParams.getOrderId()));
                objToBossReq.setQuotationHeadIds(Lists.newArrayList(quoBaseResponse.getData().get(0).getId()));
                objToBossReq.setCheckType(1);
                objToBossReq.setToBossType(2);
                BaseResponse toBossCheckResp=orderService.toBossCheckNew(objToBossReq);
                logger.info("save refBossOrder toBossCheck,rsp:{}",JSON.toJSONString(toBossCheckResp));
                if(toBossCheckResp!=null&&toBossCheckResp.getStatus()==200){
                    isTriggerInvoiceConfirm=true;
                }*/
                isTriggerInvoiceConfirm = true;
            }
            QuotationHeadRsp objQuotationHeadRsp = quotationHeadRspList.get(0);
            //有confirm的版本并且没有toboss才允许录入bossorder
            //GPO2-15160 confirm版本必须为最新版本并且没有toboss才允许录入bossorder
            if (objQuotationHeadRsp.getStatus().intValue() != 5
                    && objQuotationHeadRsp.getConfirmQuotationVersion() != null
                    && objQuotationHeadRsp.getConfirmQuotationVersion().intValue() != 0
                    && (objQuotationHeadRsp.getToBossFlag() == null || objQuotationHeadRsp.getToBossFlag().intValue() == 0)
                    && Func.equalsSafe(objQuotationHeadRsp.getConfirmQuotationVersion(), objQuotationHeadRsp.getQuotationVersion())) {
                BossQuotationRelationship objBossQuotationRelationship = new BossQuotationRelationship();
                objBossQuotationRelationship.setBossOrderNo(newBossOrderNo);
                objBossQuotationRelationship.setBossInvoiceNo(newRefInvoiceNo);
                objBossQuotationRelationship.setQuotationNos(Lists.newArrayList(objQuotationHeadRsp.getQuotationNo()));

                DoToBossRequest objDoToBossRequest = new DoToBossRequest();
                objDoToBossRequest.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                objDoToBossRequest.setSystemId(SgsSystem.GPO.getSgsSystemId());
                objDoToBossRequest.setBossQuotationRelationships(Lists.newArrayList(objBossQuotationRelationship));
                objDoToBossRequest.setSgsToken(SecurityUtil.getSgsToken());
                objDoToBossRequest.setTriggerInvoiceConfirm(isTriggerInvoiceConfirm);
                objDoToBossRequest.setToBossType(ToBossType.OFFLINE.getCode());
                BaseResponse<Boolean> baseResponse = quotationFacade.doToBoss(objDoToBossRequest);
                if (baseResponse == null || baseResponse.getStatus() != 200) {
                    this.reBackBossOrderNo(oldSlOder);
                }
                //logger.info("call quotation.doToboss rsp:{}", JSON.toJSONString(baseResponse));
            } else if (isChange) {//如果有变化如果不满足一下条件需要提示用户
                if (objQuotationHeadRsp.getConfirmQuotationVersion() == null || objQuotationHeadRsp.getConfirmQuotationVersion().intValue() == 0) {
                    this.reBackBossOrderNo(oldSlOder);
                    throw new Exception("不允许直接录入/删除Ref Boss Order No和Ref Boss Invoice No,请先Confirm Quotation");
                }
                if (objQuotationHeadRsp.getToBossFlag() != null && objQuotationHeadRsp.getToBossFlag().intValue() != 0) {
                    this.reBackBossOrderNo(oldSlOder);
                    throw new Exception("已经toBoss,不允许直接录入/删除Ref Boss Order No和Ref Boss Invoice No");
                }
                if (objQuotationHeadRsp.getStatus().intValue() == 5) {
                    this.reBackBossOrderNo(oldSlOder);
                    throw new Exception("报价状态为Closed,不允许直接录入/删除Ref Boss Order No和Ref Boss Invoice No");
                }
                if(!Func.equalsSafe(objQuotationHeadRsp.getConfirmQuotationVersion(),objQuotationHeadRsp.getQuotationVersion())){
                    this.reBackBossOrderNo(oldSlOder);
                    throw new Exception("最新版本的Quotation不为Confirm状态，请先Confirm");
                }
            }

        } else {//如果发生变化且quotation>1||quotation==0则提示用户
            if (isChange) {
                if(Func.equalsSafe(header.getSystemId(),SgsSystem.SODA.getSgsSystemId())){
                    oldSlOder.setBossOrderNo(newBossOrderNo);
                    oldSlOder.setRefBossInvoiceNo(newRefInvoiceNo);
                }
                this.reBackBossOrderNo(oldSlOder);
                if(!Func.equalsSafe(header.getSystemId(),SgsSystem.SODA.getSgsSystemId())){
                    if (Func.isEmpty(quotationHeadRspList)) {
                        throw new Exception("Order没有有效Quotation不允许直接录入/删除Ref Boss Order No和Ref Boss Invoice No");
                    } else {
                        throw new Exception("Order有多个有效Quotation不允许直接录入/删除Ref Boss Order No和Ref Boss Invoice No，请使用Excel更新.");
                    }
                }
            } else {
                this.reBackBossOrderNo(oldSlOder);
            }
        }
    }

    private void reBackBossOrderNo(SlOrderInfoPO oldSlorder) {
        if (Func.isNotEmpty(oldSlorder)) {
            slOrderMapper.updateRefBossOrderNo(oldSlorder);
        }
    }

    private Boolean isCalculateDueDate(OrderDetailInfo oldOrder, SlOrderInfoPO oldSlOrder, OrderHeaderInfo newOrder) {
        if (Func.isEmpty(oldOrder) || Func.isEmpty(oldSlOrder)) {
            return true;
        }
        if (Func.isEmpty(newOrder)) {
            return false;
        }
        if (Func.isEmpty(newOrder.getServiceLevel()) || Func.isEmpty(newOrder.getSampleConfirmDate()) || Func.isEmpty(newOrder.getTat())) {
            return false;
        }
        if (Func.isEmpty(newOrder.getExpectedOrderDueDate())) {
            return true;
        }
        return !(Func.equalsSafe(oldOrder.getServiceLevel(), newOrder.getServiceLevel())
                && Func.equalsSafe(oldOrder.getTat(), newOrder.getTat())
                && Func.equalsSafe(oldSlOrder.getSampleConfirmDate(), newOrder.getSampleConfirmDate()));
    }

    private Boolean dueDateChange(OrderDetailInfo oldOrder, OrderHeaderInfo newOrder) {
        if (Func.isEmpty(oldOrder)) {
            return true;
        }
        if (Func.isEmpty(newOrder)) {
            return false;
        }
        return Func.isNotEmpty(newOrder.getExpectedOrderDueDate()) &&
                !Func.equalsSafe(newOrder.getExpectedOrderDueDate(), oldOrder.getExpectedOrderDueDate());
    }

    private void updateSubcontractFee(SlOrderInfoPO slOrderInfo) {
        if (Func.isNotEmpty(slOrderInfo) && Func.isNotEmpty(slOrderInfo.getOrderId())) {
            slOrderMapper.updateSubcontractFee(slOrderInfo);
        }
    }

    private List<Integer> getAllService() {
        List<Integer> serviceList = new ArrayList<>();
        serviceList.add((int) OrderBizType.Attachment.getStatus());
        serviceList.add((int) OrderBizType.CareLabel.getStatus());
        serviceList.add((int) OrderBizType.CrossLab.getStatus());
        serviceList.add((int) OrderBizType.Customer.getStatus());
        serviceList.add((int) OrderBizType.ExternalNoRel.getStatus());
        serviceList.add((int) OrderBizType.Lab.getStatus());
        serviceList.add((int) OrderBizType.OrderPerson.getStatus());
        serviceList.add((int) OrderBizType.ReportReceiver.getStatus());
        serviceList.add((int) OrderBizType.Report.getStatus());
        serviceList.add((int) OrderBizType.Order.getStatus());
        serviceList.add((int) OrderBizType.ObjectTag.getStatus());
        serviceList.add((int) OrderBizType.TrfRelationship.getStatus());
        serviceList.add((int) OrderBizType.Parcel.getStatus());
        serviceList.add((int) OrderBizType.ProductSample.getStatus());
        serviceList.add((int) OrderBizType.Product.getStatus());
        serviceList.add((int) OrderBizType.SL_Order.getStatus());
        serviceList.add((int) OrderBizType.TestRequestContacts.getStatus());
        serviceList.add((int) OrderBizType.TestRequest.getStatus());
        serviceList.add((int) OrderBizType.TrfTodo.getStatus());
        serviceList.add((int) OrderBizType.OrderExt.getStatus());
        return serviceList;
    }

    /**
     * 更新Order invoiceConfirmDate并发送InvoiceConfirm给tracking
     *
     * @param orderNoList
     * @param regionAccount
     * @return
     */
    public BaseResponse updateInvoiceConfirmDate(List<String> orderNoList, String regionAccount) {
        if (Func.isEmpty(orderNoList)) {
            return BaseResponse.newFailInstance("Param OrderNo is Required");
        }
        // TODO-Trevor.Yuan 2022/6/8
        //更新invoiceConfirmDate
        OrderInfoPO orderInfoPO = new OrderInfoPO();
        orderInfoPO.setInvoiceConfirmDate(new Date());
        OrderInfoExample orderInfoExample = new OrderInfoExample();
        orderInfoExample.createCriteria().andOrderNoIn(orderNoList);
        int i = orderInfoMapper.updateByExampleSelective(orderInfoPO, orderInfoExample);
        String sgsToken = SecurityUtil.getSgsToken();
        if (i > 0) {
            for (String orderNo : orderNoList) {
                trackingService.sendInvoiceConfirm(orderNo, sgsToken, regionAccount, 0);
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }

    /**
     * 月结客户更新enableDelivery
     *
     * @param orderId
     */
    public void updateEnableDeliveryByOrderId(String orderId) {
        OrderInfoPO orderInfoPO = orderInfoMapper.selectByPrimaryKey(orderId);
        if (Func.isEmpty(orderInfoPO)) {
            return;
        }
        Integer enableDelivery = this.buildEnableDeliveryByOrderId(orderId, orderInfoPO.getPayStatus());
        orderInfoPO.setEnableDelivery(enableDelivery);
        orderInfoMapper.updateByPrimaryKeySelective(orderInfoPO);
    }


    public Integer buildEnableDeliveryByOrderId(String orderId, Integer paymentStatus) {
        Integer enableDelivery = EnableDeliveryEnums.DEFAULT_VALUE.getStatus();
        OrderInfoPO orderInfoPO = orderInfoMapper.selectByPrimaryKey(orderId);
        if (Func.isEmpty(orderInfoPO)) {
            return enableDelivery;
        }


        String paymentTermName = "";
        //查询客户信息
        CustomerInfoExample customerInfoExample = new CustomerInfoExample();
        customerInfoExample.createCriteria().andGeneralOrderIDEqualTo(orderId).andCustomerUsageEqualTo(CustomerUsage.Payer.getCode());
        List<CustomerInfoPO> customerInfoPOS = customerInfoMapper.selectByExample(customerInfoExample);
        if (Func.isNotEmpty(customerInfoPOS)) {
            paymentTermName = customerInfoPOS.get(0).getPaymentTermName();
        }
        if (Func.isNotEmpty(paymentTermName)
                && !Func.equals("IMMEDIATE", paymentTermName)) {
            //更新为月结
            enableDelivery = orderInfoPO.getEnableDelivery() | EnableDeliveryEnums.MONTHLY_PAYMENT.getStatus();
        } else if (EnableDeliveryEnums.check(orderInfoPO.getEnableDelivery(), EnableDeliveryEnums.MONTHLY_PAYMENT)) {
            enableDelivery = orderInfoPO.getEnableDelivery() ^ EnableDeliveryEnums.MONTHLY_PAYMENT.getStatus();
        } else {
            enableDelivery = orderInfoPO.getEnableDelivery();
        }
        if (Func.isEmpty(paymentStatus)) {
            paymentStatus = orderInfoPO.getPayStatus();
        }
        if (Func.isEmpty(paymentStatus)) {
            paymentStatus = PaymentStatus.UN_PAID.getType();
        }
        /**
         * 判断paymentStatus是否NA
         * NA存在两种场景
         * 1.非免单，totalPrice为0,paymentStatus是NA
         * 2.免单申请未审批时，totalPrice为0,paymentStatus也是NA
         */
        List<OrderQuotationDTO> orderQuotationDTOS = orderMapper.getQuotationOrderForOrderId(orderId);
        boolean allQuotationFree = true;
        if (Func.isNotEmpty(orderQuotationDTOS)) {
            long count = orderQuotationDTOS.stream().filter(item -> Func.isEmpty(item.getQuotationFlag()) || !QuotationFlag.hasFreeQuotationApprovedFlags(item.getQuotationFlag())).count();
            //存在非免单或免单未审批的
            if (count > 0) {
                logger.info("calc enableDelivery,{},exists unApproved free quotation", orderInfoPO.getOrderNo());
                allQuotationFree = false;
            }
        }

        logger.info("calc enableDelivery,{},payStatus:{}", orderInfoPO.getOrderNo(),PaymentStatus.getType(paymentStatus).getCode());
        if (PaymentStatus.check(paymentStatus, PaymentStatus.NA)) {
            //NA 且所有的quotation都免单了
            if (allQuotationFree) {
                enableDelivery = enableDelivery | EnableDeliveryEnums.FREE_ORDER.getStatus();
            } else {
//                NA 但免单申请未审批
                if (EnableDeliveryEnums.check(enableDelivery, EnableDeliveryEnums.FREE_ORDER)) {
                    enableDelivery = enableDelivery ^ EnableDeliveryEnums.FREE_ORDER.getStatus();
                }
            }
        } else {
            Map<Integer, Boolean> result = buParamService.reportDeliveryWithPaymentStatus(new HashSet<>(Arrays.asList(paymentStatus)));
            Boolean parPaidSendEmail = (Func.isNotEmpty(result) && result.containsKey(paymentStatus)) ? result.get(paymentStatus) : false;
            if (parPaidSendEmail) {
                enableDelivery = enableDelivery | EnableDeliveryEnums.UPLOAD_PAID_UP.getStatus();
            } else if (EnableDeliveryEnums.check(enableDelivery, EnableDeliveryEnums.UPLOAD_PAID_UP)) {
                enableDelivery = enableDelivery ^ EnableDeliveryEnums.UPLOAD_PAID_UP.getStatus();
            }else if (EnableDeliveryEnums.check(enableDelivery, EnableDeliveryEnums.FREE_ORDER)) {
                enableDelivery = enableDelivery ^ EnableDeliveryEnums.FREE_ORDER.getStatus();
            }
        }
        logger.info("calc enableDelivery,{},after calc enableDelivery Status:{}", orderInfoPO.getOrderNo(),enableDelivery);
        return enableDelivery;
    }

    /**
     * 、
     *
     * @param reqObject
     * @return
     */
    public CustomResult toTestOrderInfo(OrderDetailDto reqObject) {
        UserInfo localUser = tokenClient.getUser(reqObject.getToken());

        CustomResult<OrderDetailRsp> rspResult = this.saveOrderInfo(reqObject);
        if (!rspResult.isSuccess()) {
            return rspResult;
        }
        OrderDetailRsp order = rspResult.getData();
        /**
         * TODO 实现同步
         * http://cnesgo-uat.sgs.net/OTSNotesApi/Order/api/v1/createOrder
         */
        //组织参数
        SyncOrderInfo syncOrderInfo = buildSyncOrder(reqObject);
        logger.info("createOrderInfo:{}", JSON.toJSONString(syncOrderInfo));
        rspResult = orderClient.createOrderInfo(syncOrderInfo);
        logger.info("createOrderInfo Res:{}", JSON.toJSONString(rspResult));
        if (!rspResult.isSuccess()) {
            return rspResult;
        }
        //sync to quotation
        SyncSampleInfoRequest objSyncSampleInfoRequest = new SyncSampleInfoRequest();
        objSyncSampleInfoRequest.setOrderId(order.getOrderId());
        List<SampleInfo> sampleInfoList = Func.copy(syncOrderInfo.getSamples(), SyncSampleInfo.class, SampleInfo.class);
        objSyncSampleInfoRequest.setSampleInfoList(sampleInfoList);
        objSyncSampleInfoRequest.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        objSyncSampleInfoRequest.setSystemId(SgsSystem.GPO.getSgsSystemId());
        BaseResponse<Boolean> objQuotationFacadeRes = quotationMatrixFacade.syncSampleInfo(objSyncSampleInfoRequest);
        logger.info("syncSampleInfo Res:{}", JSON.toJSONString(objQuotationFacadeRes));
        if (objQuotationFacadeRes == null || !objQuotationFacadeRes.getData()) {
            rspResult.setMsg("Sync sample to quotation is failed!");
            rspResult.setSuccess(false);
            return rspResult;
        }
        /**
         * TODO 更新 toTestFlag 标识
         * http://cnesgo-uat.sgs.net/OrderApi//order/updateTestFlag
         */
        OrderInfoPO orderInfoPO = new OrderInfoPO();
        orderInfoPO.setID(order.getOrderId());
        orderInfoPO.setToTestFlag(1);
        orderInfoPO.setModifiedBy(localUser.getRegionAccount());
        orderInfoPO.setModifitedDate(DateUtils.getNow());
        orderInfoPO.setToTestVersion(ToTestHashHelper.hashSyncOrder(syncOrderInfo));
        orderMapper.updateToTestFlag(orderInfoPO);

        return rspResult;
    }

    public BaseResponse<List<TestLineLabSectionRsp>> beforeSaveOrderInfo(OrderDetailDto reqObject) {
        if (Func.isEmpty(reqObject) || Func.isEmpty(reqObject.getHeaders())) {
            return BaseResponse.newFailInstance(ResponseCode.PARAM_VALID_ERROR);
        }
        BeforeSaveOrderRsp beforeSaveOrderRsp = new BeforeSaveOrderRsp();
        //是否提示TestLineLabSection Dialog
        boolean labSectionMultiple = false;
        boolean labSectionSelectAll = false;
        BuParamValueRsp buParamValue = frameWorkClient.getBuParamValue(ProductLineContextHolder.getProductLineCode(), "", Constants.BU_PARAM.TestLine.GROUP, Constants.BU_PARAM.TestLine.LABSECTION_SELECT_MULTIPLE.CODE);
        if (Func.isNotEmpty(buParamValue) && StringUtils.equalsIgnoreCase(Constants.BU_PARAM.TestLine.LABSECTION_SELECT_MULTIPLE.VALUES.TRUE, buParamValue.getParamValue())) {
            labSectionMultiple = true;
        }
        buParamValue = frameWorkClient.getBuParamValueByLab(ProductLineContextHolder.getProductLineCode(), reqObject.getLab().getLabCode(), Constants.BU_PARAM.TestLine.GROUP, Constants.BU_PARAM.TestLine.LABSECTION_SELECT_ALL.CODE);
        if (Func.isNotEmpty(buParamValue) && StringUtils.equalsIgnoreCase(Constants.BU_PARAM.TestLine.LABSECTION_SELECT_ALL.VALUES.TRUE, buParamValue.getParamValue())) {
            labSectionSelectAll = true;
        }

        if (labSectionMultiple && labSectionSelectAll) {
            String orderNo = reqObject.getHeaders().getOrderNo();
            List<CrossLabRelPO> crossLabRelPOS = crossLabService.getToLabByOrder(orderNo);
            String oldShareToLab = "";
            if (CollectionUtils.isNotEmpty(crossLabRelPOS)) {
                oldShareToLab = crossLabRelPOS.get(0).getToLab();
            }
            String newShareToLab = reqObject.getHeaders().getShareToLab();

            QueryTestLineLabSectionReq queryTestLineLabSectionReq = new QueryTestLineLabSectionReq();
            queryTestLineLabSectionReq.setOrderNo(orderNo);
            queryTestLineLabSectionReq.setOldTopsToLab(oldShareToLab);
            queryTestLineLabSectionReq.setNewTopsToLab(newShareToLab);
            queryTestLineLabSectionReq.setConfirmOrderFlag(false);
            queryTestLineLabSectionReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            queryTestLineLabSectionReq.setToken(tokenClient.getToken());
            BaseResponse<List<TestLineLabSectionRsp>> testLineLabSectionRes = testLineFacade.queryAssignLabSectionTLList(queryTestLineLabSectionReq);
            if (Func.isNotEmpty(testLineLabSectionRes) && Func.isNotEmpty(testLineLabSectionRes.getData())) {
                List<com.sgs.preorder.facade.model.rsp.TestLineLabSectionRsp> testLineLabSectionRspList = new ArrayList<>();
                for (TestLineLabSectionRsp testLineLabSectionRsp : testLineLabSectionRes.getData()) {
                    com.sgs.preorder.facade.model.rsp.TestLineLabSectionRsp target = new com.sgs.preorder.facade.model.rsp.TestLineLabSectionRsp();
                    Func.copy(testLineLabSectionRsp, target);
                    testLineLabSectionRspList.add(target);
                }
                beforeSaveOrderRsp.setTestLineLabSectionRspList(testLineLabSectionRspList);
            }
        }
        return BaseResponse.newSuccessInstance(beforeSaveOrderRsp);
    }

    /**
     * @param reqObject
     * @return
     */
    public CustomResult saveOrderInfoToTest(OrderDetailDto reqObject) {
        //先查询一次OrderDueDate
        OrderInfoDto oldOrderDTO = orderMapper.getOrderInfo(reqObject.getHeaders().getOrderId());
        Date oldExpectedOrderDueDate = null;
        if (Func.isNotEmpty(oldOrderDTO)) {
            oldExpectedOrderDueDate = oldOrderDTO.getExpectedOrderDueDate();
        }
        CustomResult checkResult = new CustomResult();
        BaseResponse<String> checkProductSampleResponse = this.checkProductSample(reqObject.getProductSampleRspList());
        if (checkProductSampleResponse.isFail()) {
            return checkResult.fail(checkProductSampleResponse.getMessage());
        }
        CustomResult result = saveOrderInfo(reqObject);
        if (!result.isSuccess()) {
            result.setMsg("Fail to Save " + StringUtils.trimToEmpty(result.getMsg()));
            return result;
        }
        //更新ReportCountry
        BaseResponse<List<String>> updateReportCountryResponse = this.updateReportCountry(reqObject.getHeaders().getOrderNo(), reqObject.getProductSampleRspList());


        String token = reqObject.getToken();
        UserInfo user = tokenClient.getUser(token);
        UserHelper.setLocalUser(user);

        // charge order 订单 没有sample，也没有testLine ,下面的逻辑不用走
        if (OperationModeEnum.check(reqObject.getOperationMode(), OperationModeEnum.ChargeOnly)) {
            return result;
        }
        OrderDetailRsp detailRsp = (OrderDetailRsp) result.getData();

        OrderInfoDto order = orderMapper.getOrderInfo(detailRsp.getOrderId());

        if (!OrderStatus.checkStatus(order.getOrderStatus(),
                OrderStatus.New,
                OrderStatus.Confirmed,
                OrderStatus.Testing,
                OrderStatus.Reporting)) {
            logger.info("OrderDetailService.saveOrderInfoToTest 不符合业务规则：Order只有存在一个状态为New/confirmed/testing，则可以支持To Test，orderNo:{}", detailRsp.getOrderNo());
            return result;
        }
        SyncOrderInfo syncOrderInfo = buildSyncOrder(reqObject);

        int newToTestVersion = 0;
        if (Func.isEmpty(reqObject.getSource())) {
            if (OrderStatus.checkStatus(order.getOrderStatus(), OrderStatus.Reporting)) {
                if (!(checkReportToTest(detailRsp.getOrderId()))) {
                    logger.info("OrderDetailService.saveOrderInfoToTest 不符合业务规则：OrderStatus Reporting且 不包含ReportStatus为New的report，则不支持To Test，orderNo:{}", detailRsp.getOrderNo());
                    return result;
                }
            }

            // 是否已ToTest
            boolean toTestFlag = NumberUtil.equals(NumberUtil.toInt(order.getToTestFlag()), 1);
            if (!toTestFlag) {
                return result;
            }
            int oldToTestVersion = Func.notNull(order.getToTestVersion()) ? order.getToTestVersion() : 0;
            newToTestVersion = ToTestHashHelper.hashSyncOrder(syncOrderInfo);

            // 当用户输入Sample时，点Save，这时用户再次进入时不修改任何数据，直接点To Test按钮，这里就需要加：NumberUtil.equals(toTestFlag, 1)
            if (toTestFlag && oldToTestVersion == newToTestVersion) {
                logger.info("OrderDetailService.saveOrderInfoToTest 数据未变更，不需要ToTest，orderNo:{}", detailRsp.getOrderNo());
                if (updateReportCountryResponse.isSuccess() && Func.isNotEmpty(updateReportCountryResponse.getData())) {
                    result.setMsg("CountryOfDestination被Report[" + Func.join(updateReportCountryResponse.getData(), ",") + "]使用");
                    result.setData(1);
                }
                return result;
            }
        }


        CustomResult orderInfoResult = orderClient.createOrderInfo(syncOrderInfo);
        if (!orderInfoResult.isSuccess()) {
            return result.fail("Fail to Test " + orderInfoResult.getMsg());
        }

        String userName = "";
        if (user != null) {
            userName = user.getRegionAccount();
        }
        OrderInfoPO orderInfoPO = new OrderInfoPO();
        orderInfoPO.setID(detailRsp.getOrderId());
        orderInfoPO.setToTestFlag(1);
        orderInfoPO.setModifiedBy(userName);
        orderInfoPO.setModifitedDate(DateUtils.getNow());
        orderInfoPO.setToTestVersion(newToTestVersion);
        orderMapper.updateToTestFlag(orderInfoPO);
        result.setSuccess(true);
        if (updateReportCountryResponse.isSuccess() && Func.isNotEmpty(updateReportCountryResponse.getData())) {
            result.setMsg("CountryOfDestination被Report[" + Func.join(updateReportCountryResponse.getData(), ",") + "]使用");
            result.setData(1);
        }
        return result;
    }

    private BaseResponse<List<String>> updateReportCountry(String orderNo, List<ProductSampleRsp> productSampleRspList) {
        List<String> resultReportNoList = new ArrayList<>();
        try {
            UpdateReportCountryOfDestinationReq updateReportCountryOfDestinationReq = new UpdateReportCountryOfDestinationReq();
            List<ReportCountryOfDestinationDTO> updateReportCountryOfDestinationList = new ArrayList<>();
            for (ProductSampleRsp newProduct : productSampleRspList) {
                ProductInfo product = newProduct.getProduct();
                if (product == null) {
                    continue;
                }
                product.setLanguageID(newProduct.getLanguageID());
                String countryOfDestination = product.getCountryOfDestination();
                if (Func.isNotEmpty(countryOfDestination)) {
                    List<String> countryOfDestinationList = Func.toStrList(",", countryOfDestination);
                    for (String s : countryOfDestinationList) {
                        ReportCountryOfDestinationDTO reportCountryOfDestination = new ReportCountryOfDestinationDTO();
                        reportCountryOfDestination.setLanguageId(newProduct.getLanguageID());
                        reportCountryOfDestination.setValue(s);
                        updateReportCountryOfDestinationList.add(reportCountryOfDestination);
                    }
                }
            }
            updateReportCountryOfDestinationReq.setReportCountryOfDestinationList(updateReportCountryOfDestinationList);
            updateReportCountryOfDestinationReq.setOrderNo(orderNo);
            BaseResponse<List<String>> updateReportCountryRes = reportFacade.updateReportCountryOfDestination(updateReportCountryOfDestinationReq);
            if (updateReportCountryRes.isSuccess() && Func.isNotEmpty(updateReportCountryRes.getData())) {
                resultReportNoList = updateReportCountryRes.getData();
            }
        } catch (Exception e) {
            logger.error("Update ReportCountryOfDestination error :{}", e);
        }
        return BaseResponse.newSuccessInstance(resultReportNoList);
    }

    private boolean checkReportToTest(String orderId) {
        List<ReportInfoPO> reportList = reportService.getReportListByOrderId(orderId);
        for (ReportInfoPO report : reportList) {
            if (ReportStatus.checkStatus(report.getReportStatus(), ReportStatus.New)) {
                return true;
            }
        }
        return false;
    }


    private SyncOrderInfo buildSyncOrder(OrderDetailDto reqObject) {
        OrderHeaderInfo orderHeader = reqObject.getHeaders();
        //组织参数
        SyncOrderInfo syncOrderInfo = new SyncOrderInfo();
        // POSL-3557  多次to test时，切换Lab code，otsNotes和preorder的lab不一致的问题，要和order保存的一致
        LabInfoPO labInfoPO = labMapper.getLabInfoByOrderId(orderHeader.getOrderId());
        com.sgs.otsnotes.facade.model.info.user.UserLabBuInfo labBuInfo = new com.sgs.otsnotes.facade.model.info.user.UserLabBuInfo();
//        BeanUtils.copyProperties(reqObject.getLab(), labBuInfo);
        labBuInfo.setLabId(labInfoPO.getLabID());
        labBuInfo.setLabCode(labInfoPO.getLabCode());
        labBuInfo.setProductLineId(labInfoPO.getProductLineID());
        labBuInfo.setLocationId(labInfoPO.getLocationID());
//        syncOrderInfo.setRefSystemId(orderHeader.getReferenceId());
        List<Integer> refSystemIdList = new ArrayList<>();
        if (Func.isNotEmpty(orderHeader.getReferences())) {
            refSystemIdList = ReferenceConvertor.toRefSystemIdList(orderHeader.getReferences()).stream().collect(Collectors.toList());
        }
        syncOrderInfo.setRefSystemIdList(refSystemIdList);
        syncOrderInfo.setLabBu(labBuInfo);
        syncOrderInfo.setOrderNo(orderHeader.getOrderNo());

        ReportInfo report = reqObject.getReport();
        if (report != null) {
            syncOrderInfo.setReportId(report.getId());
            syncOrderInfo.setReportNo(report.getReportNo());
        }
        syncOrderInfo.setReportDueDate(reqObject.getHeaders().getReportExpectDueDate());
        syncOrderInfo.setToken(reqObject.getToken());
        //customer数据
        SyncCustomerInfo customerInfo = orderMapper.getOrderCustomerInfo(orderHeader.getOrderNo());
        syncOrderInfo.setCustomer(customerInfo);
        //sample数据
        List<SyncSampleInfo> samples = productMapper.getSyncSamplesbyOrderId(orderHeader.getOrderId());
        logger.info("排序后的sample:{}",JSON.toJSONString(samples));
        if(Func.isNotEmpty(samples) && Func.equalsSafe(labInfoPO.getProductLineCode(), ProductLineType.MR.getProductLineAbbr())){
            //MR 特殊逻辑 重新设置Description 为扩展字段中
            samples.stream().forEach(sample -> {
                if(Func.isNotEmpty(sample.getExtFields())){
                    JSONObject extFieldsJSON = JSONObject.parseObject(sample.getExtFields());
                    sample.setDescription(StringPool.EMPTY);
                    if(Func.isNotEmpty(extFieldsJSON.get("sampleDescription"))){
                        sample.setDescription(extFieldsJSON.get("sampleDescription").toString());
                    }
                }
            });
        }
        syncOrderInfo.setSamples(samples);
        syncOrderInfo.setQrcodeFlag(reqObject.getTestRequest().getQrcodeFlag());

        if (CollectionUtils.isNotEmpty(reqObject.getTestLines())) {
            List<SyncTestLineInfo> testLineInfos = Lists.newArrayList();
            for (TestLineInfoDto saveTestLineInfo : reqObject.getTestLines()) {
                SyncTestLineInfo syncTestLineInfo = new SyncTestLineInfo();
                BeanUtils.copyProperties(saveTestLineInfo, syncTestLineInfo);
                testLineInfos.add(syncTestLineInfo);
            }
            // 去重
            syncOrderInfo.setTestLineInfos(testLineInfos.stream().distinct().collect(Collectors.toList()));
        }
        syncOrderInfo.setTestMatrixList(reqObject.getTestMatrixList());

        List<ReportInfo> reportInfoList = reqObject.getReports();
        //兼容只有一个report时
        if (Func.isEmpty(reportInfoList)
                && Func.isNotEmpty(reqObject.getReport())) {
            reportInfoList = Lists.newArrayList();
            reqObject.setReports(reportInfoList);
            reportInfoList.add(reqObject.getReport());
        }
        if (Func.isNotEmpty(reportInfoList)) {
            List<OrderReportDto> reportList = reportInfoList.parallelStream().map(reportInfo -> {
                OrderReportDto orderReportDto = new OrderReportDto();
                orderReportDto.setOrderId(orderHeader.getOrderId());
                orderReportDto.setOrderNo(orderHeader.getOrderNo());
                orderReportDto.setReportId(reportInfo.getId());
                orderReportDto.setReportNo(reportInfo.getReportNo());
                orderReportDto.setExternalReportNo(reportInfo.getExternalReportNo());
                orderReportDto.setReportMatrix(reportInfo.getReportMatrix());
                orderReportDto.setExpectDueDate(orderHeader.getReportExpectDueDate());
                return orderReportDto;
            }).collect(Collectors.toList());
            syncOrderInfo.setReportList(reportList);
        }
        // SealFlag同步
        TestRequestInfo testRequestInfo = testRequestMapper.getTestRequestByOrderId(orderHeader.getOrderId());
        if (Func.isNotEmpty(testRequestInfo) && Func.isNotEmpty(testRequestInfo.getSealFlag())) {
            syncOrderInfo.setSealFlag(testRequestInfo.getSealFlag());
        }
        return syncOrderInfo;
    }

    /**
     * @param productSamples
     * @return
     */
    private BaseResponse<String> checkProductSample(List<ProductSampleRsp> productSamples) {
        Map<String, List<ProductSampleInfo>> productSampleMap = new HashMap<>();
        if(Func.isNotEmpty(productSamples)){
            for (ProductSampleRsp productSample : productSamples) {
                for (ProductSampleInfo productSampleInfo : productSample.getProductSamples()) {
                    //productItem key
                    String key = productSampleInfo.getProductItemNo();
                    if (productSampleMap.containsKey(key)) {
                        productSampleMap.get(key).add(productSampleInfo);
                    } else {
                        List<ProductSampleInfo> productSampleInfoList = new ArrayList<>();
                        productSampleInfoList.add(productSampleInfo);
                        productSampleMap.put(key, productSampleInfoList);
                    }
                    if (StringUtils.isBlank(productSampleInfo.getSampleID())) {
                        return BaseResponse.newFailInstance("Fail to Test Product Sample No can not be null!");
                    }
                }
            }
            // 检查多语言的sample qty 是否一直
            for (Map.Entry<String, List<ProductSampleInfo>> entry : productSampleMap.entrySet()) {
                List<ProductSampleInfo> productSampleInfoList = entry.getValue();
                if(productSampleInfoList.size() > 1){
                    Integer qty = productSampleInfoList.get(0).getNoOfSample();
                    for (int i = 1; i < productSampleInfoList.size(); i++) {
                        if(!Func.equalsSafe(qty,productSampleInfoList.get(i).getNoOfSample())){
                            return BaseResponse.newFailInstance("Fail to Test Product Sample qty is not same!");
                        }
                    }
                }
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }

    // 自动保存标签并且返回更新后的JSON
    public String saveTagValue(String tagJson, String productLineCode, String tagObjectType, List<TagValueInfoPO> newTagValues) {
//        if(Func.isEmpty(newTagValues)){
//            newTagValues = Lists.newArrayList();
//        }
        if (StringUtils.isNotEmpty(tagJson)) {
            List<TagValueSelectDTO> tags = JSONObject.parseObject(tagJson, new TypeReference<List<TagValueSelectDTO>>() {
            });
            if (CollectionUtils.isNotEmpty(tags)) {
                List<TagSearchDTO> allTags = tagService.searchTagList(ProductLineContextHolder.getProductLineCode(), tagObjectType, null);

                Map<String, List<TagValueDTO>> tagsValueMap = allTags.stream().filter(item -> Func.isNotEmpty(item.getTagValues())).collect(Collectors.toMap(TagSearchDTO::getTagId, TagSearchDTO::getTagValues));
                List<TagValueInfoPO> finalNewTagValues = newTagValues;
                tags.forEach(tag -> {
                    List<String> values = Lists.newArrayList();
                    TagInfoPO tagInfoPO = tagMapper.selectByPrimaryKey(tag.getTagId());
//                    List<TagValueDTO> newTagValues = Lists.newArrayList();
                    List<TagValueDTO> tagValues = tagsValueMap.get(tag.getTagId());
                    if (Func.isNotEmpty(tagInfoPO) && Func.equalsSafe(tagInfoPO.getIsSave(), Byte.valueOf("1")) && Func.isNotEmpty(tag.getSelectedValue())) {
                        tag.getSelectedValue().forEach(val -> {
                            //判断当前的tag 值是不是新增的
                            Boolean isNew = true;
                            String tagValueId = "";
                            if (Func.isNotEmpty(tagValues) && (null != tagValues.parallelStream().filter(t -> t.getId().equals(val)).findAny().orElse(null))) {
                                isNew = false;
                                tagValueId = val;
                            } else {
                                TagValueInfoExample example = new TagValueInfoExample();
                                example.createCriteria().andTagIdEqualTo(tag.getTagId()).andTagValueEqualTo(val).andProductLineCodeEqualTo(productLineCode);
                                List<TagValueInfoPO> tagValueInfoPOS = tagValueMapper.selectByExample(example);
                                if (CollectionUtils.isNotEmpty(tagValueInfoPOS)) {
                                    isNew = false;
                                    tagValueId = tagValueInfoPOS.get(0).getId();
                                }
                            }
                            if (isNew) {
                                TagValueInfoPO tagValueInfoPO = new TagValueInfoPO();
                                tagValueInfoPO.setTagId(tag.getTagId());
                                tagValueInfoPO.setProductLineCode(productLineCode);
                                tagValueInfoPO.setTagValue(val);
                                tagValueId = Func.randomUUID();
                                tagValueInfoPO.setId(tagValueId);
                                tagValueInfoPO.setCreatedBy(tokenClient.getUser().getRegionAccount());
                                tagValueInfoPO.setCreatedDate(DateUtils.now());
                                tagValueInfoPO.setActiveIndicator(new Byte("1"));
                                tagValueInfoPO.setSequence(0);
                                finalNewTagValues.add(tagValueInfoPO);
                                values.add(tagValueId);
//                                TagValueDTO tagValueDTO = new TagValueDTO();
//                                Func.copy(tagValueInfoPO,tagValueDTO);
//                                newTagValues.add(tagValueDTO);
                            } else {
                                values.add(tagValueId);
                            }
                        });
//                        if(Func.isNotEmpty(newTagValues)){
//                            List<TagValueDTO> tagValues = tag.getTagValues();
//                            if(Func.isNotEmpty(tagValues)){
//                                newTagValues.addAll(tagValues);
//                            }
//                            tag.setTagValues(newTagValues);
//                        }
                        tag.setSelectedValue(values);
                    }
                });
//                if(CollectionUtils.isNotEmpty(newTagValues)){
//                    tagValueExtMapper.insertBatch(newTagValues);
//                }
                return JSON.toJSONString(tags);
            }
        }
        return "";
    }

    private void gpnUpdateOrder(String orderNo, String orderId, String responsibleTeamCode, String csName, String userName, String ts) {
        GpoUpdateOrderReq order = new GpoUpdateOrderReq();
        ProductLineContextHolder.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        TestRequestInfoExample testRequestInfoExample = new TestRequestInfoExample();
        testRequestInfoExample.createCriteria().andGeneralOrderIDEqualTo(orderId);
        List<TestRequestInfoPO> testRequestInfoPOS = testRequestInfoMapper.selectByExample(testRequestInfoExample);
        if (testRequestInfoPOS.size() > 0) {
            if (testRequestInfoPOS.get(0).getReturnResidueSampleFlag() != null) {
                order.setReturnSample(testRequestInfoPOS.get(0).getReturnResidueSampleFlag().shortValue());
            }
        }
        CustomerInfoExample customerInfoExample = new CustomerInfoExample();
        customerInfoExample.createCriteria().andGeneralOrderIDEqualTo(orderId);
        List<CustomerInfoPO> customerInfoPOS = customerInfoMapper.selectByExample(customerInfoExample);
        CustomerInfoPO buyer = customerInfoPOS.stream().filter(customer -> CustomerType.Buyer.getCode().equals(customer.getCustomerUsage())).findAny().orElse(null);
        CustomerInfoPO applicant = customerInfoPOS.stream().filter(customer -> CustomerType.Applicant.getCode().equals(customer.getCustomerUsage())).findAny().orElse(null);
        if (null != buyer) {
            order.setCustomerNameCn(StringUtils.isNotBlank(buyer.getCustomerNameCN()) ? buyer.getCustomerNameCN() : null);
        }
        if (null != applicant) {
            order.setApplicantCustomerGroupName(StringUtils.isNotBlank(applicant.getBuyerGroupName()) ? applicant.getBuyerGroupName() : null);
            order.setApplicantCustomerNameCN(StringUtils.isNotBlank(applicant.getCustomerNameCN()) ? applicant.getCustomerNameCN() : null);
            order.setApplicantCustomerNameEn(StringUtils.isNotBlank(applicant.getCustomerNameEN()) ? applicant.getCustomerNameEN() : null);
            order.setApplicantCustomerGroupCode(StringUtils.isNotBlank(applicant.getBuyerGroup()) ? applicant.getBuyerGroup() : null);
        }
        order.setResponsibleTeamCode(StringUtils.isNotBlank(responsibleTeamCode) ? responsibleTeamCode : null);
        order.setCsName(csName);
        order.setOrderNo(orderNo);
        order.setUserName(userName);
        order.setTechnicalSupporter(ts);
        orderFacade.GPOUpdateOrderInfo(order);
    }

    /**
     * @param orderId
     * @return
     */
    public CustomResult<OrderProductRsp> getProductSampleList(String orderId) {
        CustomResult rspResult = new CustomResult();
        if (StringUtils.isEmpty(orderId)) {
            rspResult.setSuccess(true);
            return rspResult;
        }
        OrderInfoPO orderInfoPO = orderInfoMapper.selectByPrimaryKey(orderId);
        OrderProductRsp orderProduct = new OrderProductRsp();
        List<Integer> oldProductVersionIds = Lists.newArrayList(),
                oldProductSampleVersionIds = Lists.newArrayList(),
                oldCareLabelVersionIds = Lists.newArrayList();

        List<ProductSampleRsp> productSampleRspList = Lists.newArrayList();
        // header信息
        List<ProductInfo> products = productMapper.getProductInfoByOrderId(orderId);
        if (products != null) {
            for (ProductInfo product : products) {
                logger.info("Old Product Info: {}", JSON.toJSONString(product));
                oldProductVersionIds.add(product.hashCode());
            }
        }

        // grid和careLable信息
        List<ProductSampleInfo> productSamples = productMapper.getProductSampleByOrderId(orderId);
        if (Func.isNotEmpty(productSamples)) {
            //
            productSamples.stream().forEach(productSampleInfo -> {
                if(Func.isNotEmpty(productSampleInfo.getExtFields())){
                    JSONObject extFieldsJSON = JSONObject.parseObject(productSampleInfo.getExtFields());
                    if(Func.isNotEmpty(extFieldsJSON.get("externalSampleNo"))){
                        productSampleInfo.setExternalSampleNo(extFieldsJSON.get("externalSampleNo").toString());
                    }
                    if(Func.isNotEmpty(extFieldsJSON.get("testSurfaceDirection"))){
                        productSampleInfo.setTestSurfaceDirection(extFieldsJSON.get("testSurfaceDirection").toString());
                    }
                    if(Func.isNotEmpty(extFieldsJSON.get("sampleDescription"))){
                        productSampleInfo.setSampleDescription(extFieldsJSON.get("sampleDescription").toString());
                    }
                }
            });

            //主单以及子单并且是出pdf的非new状态的sample
            List<String> disabledSampleNos = Lists.newArrayList();
            TestRequestInfo testRequestInfo = testRequestMapper.getTestRequestByOrderId(orderInfoPO.getID());
            if (testRequestInfo != null && Func.isNotEmpty(testRequestInfo.getReportRequirement())) {
                OrderNosReq nosReq = new OrderNosReq();
                nosReq.setOrderNo(orderInfoPO.getOrderNo());
                nosReq.setReportRequirement(testRequestInfo.getReportRequirement());
                BaseResponse<List<String>> disabledSampleNoResp = reportFacade.getDisabledSampleNo(nosReq);
                if (disabledSampleNoResp.getStatus() != 200) {
                    rspResult.setMsg("查询report的sample失败");
                    rspResult.setSuccess(false);
                    return rspResult;
                }
                disabledSampleNos = disabledSampleNoResp.getData();
                logger.info("disabledSampleNos Info: {}", JSON.toJSONString(disabledSampleNos));
            }

            for (ProductSampleInfo productSample : productSamples) {
                logger.info("Old ProductSample Info: {}", JSON.toJSONString(productSample));
                oldProductSampleVersionIds.add(productSample.hashCode());
            }
            long oldLanguageCount = productSamples.stream().map(p -> p.getLanguageID()).distinct().count();
            logger.info("Old ProductSample Language Info: {}", JSON.toJSONString(oldLanguageCount));
            oldProductSampleVersionIds.add(NumberUtil.toInt(oldLanguageCount));
            Map<Integer, List<ProductSampleInfo>> productSamplesLanguageMap = productSamples.stream().collect(Collectors.groupingBy(ProductSampleInfo::getLanguageID));
            for (Integer languageID : productSamplesLanguageMap.keySet()) {
                ProductSampleRsp productSample = new ProductSampleRsp();
                Map<String, CareLabelInfo> careLabels = Maps.newHashMap();
                //拿到当前language的product的header
                List<ProductInfo> languageProductInfoList = products.stream().filter(productInfo -> {
                    return productInfo.getLanguageID() == languageID;
                }).collect(Collectors.toList());
                if (Func.isEmpty(languageProductInfoList)) {
                    continue;
                }
                ProductInfo languageProductInfo = languageProductInfoList.get(0);
                productSample.setLanguageID(languageID);
                String languageCode = LanguageType.English.getCode();
                if (LanguageType.Chinese.getLanguageId() == languageID) {
                    languageCode = LanguageType.Chinese.getCode();
                }
                productSample.setLanguageCode(languageCode);
                productSample.setProduct(languageProductInfo);
                // 拿到grid和careLable
                productSamples = productSamplesLanguageMap.get(languageID);
                if (productSamples == null || productSamples.isEmpty()) {
                    productSampleRspList.add(productSample);
                    continue;
                }
                for (ProductSampleInfo sample : productSamples) {
                    List<CareLabelInfo> oldCareLabels = sample.getCareLabels();
                    if (oldCareLabels == null) {
                        continue;
                    }
                    for (CareLabelInfo oldCareLabel : oldCareLabels) {
                        if (!careLabels.containsKey(oldCareLabel.getId())) {
                            if (oldCareLabel.getProductItemNo() == null) {
                                oldCareLabel.setProductItemNo(Lists.newArrayList());
                            }
                            careLabels.put(oldCareLabel.getId(), oldCareLabel);
                        }
                        CareLabelInfo careLabel = careLabels.get(oldCareLabel.getId());
                        careLabel.getProductItemNo().add(sample.getProductItemNo());
                        //careLabel.setOldVersionId(careLabel.hashCode());
                    }
                    sample.setCareLabels(null);
                    logger.info("disabledSampleNos1 Info: {}", sample.getSampleID());
                    if (disabledSampleNos.contains(sample.getSampleID())) {
                        sample.setDisabled(true);
                    } else {
                        sample.setDisabled(false);
                    }
                }
//                productSamples.sort(new ProductSampleComparator(true));
                productSamples = productSamples.stream().sorted(Comparator.comparing(ProductSampleInfo::getSort,Comparator.nullsLast(BigDecimal::compareTo))).collect(Collectors.toList());
                logger.info("QueryDFF排序后结果：{}",JSON.toJSONString(productSamples));
                productSample.setProductSamples(productSamples);
                productSample.setCareLabels(Lists.newArrayList(careLabels.values()));
                for (CareLabelInfo careLabel : careLabels.values()) {
                    oldCareLabelVersionIds.add(careLabel.hashCode());
                }
                productSampleRspList.add(productSample);
            }
        } else if (Func.isNotEmpty(products)) {
            for (ProductInfo productInfo : products) {
                ProductSampleRsp productSample = new ProductSampleRsp();
                String languageCode = LanguageType.English.getCode();
                if (LanguageType.Chinese.getLanguageId() == productInfo.getLanguageID()) {
                    languageCode = LanguageType.Chinese.getCode();
                }
                productSample.setLanguageID(productInfo.getLanguageID());
                productSample.setLanguageCode(languageCode);
                productSample.setProduct(productInfo);
                productSample.setCareLabels(new ArrayList<>());
                productSampleRspList.add(productSample);
            }
        }
        orderProduct.setOldProductVersionId(HashUtils.hashCode(oldProductVersionIds));
        orderProduct.setOldProductSampleVersionId(HashUtils.hashCode(oldProductSampleVersionIds));
        orderProduct.setOldCareLabelVersionId(HashUtils.hashCode(oldCareLabelVersionIds));
        orderProduct.setProductSampleRspList(productSampleRspList);
        rspResult.setData(orderProduct);
        rspResult.setSuccess(true);

        return rspResult;
    }

    /**
     * @param orderId
     * @return
     */
    public CustomResult getTestRequestInfo(String orderId) {
        CustomResult rspResult = new CustomResult();
        if (StringUtils.isEmpty(orderId)) {
            rspResult.setSuccess(true);
            return rspResult;
        }
        TestRequestInfo testRequest = testRequestMapper.getTestRequestByOrderId(orderId);
        if (testRequest == null) {
            rspResult.setMsg("请求orderId Test Request信息未找到.");
            return rspResult;
        }

        rspResult.setData(testRequest);
        rspResult.setSuccess(true);

        return rspResult;
    }


    /**
     * 保存Quotation
     *
     * @param quotation
     * @return
     */
    public CustomResult saveQuotationInfo(QuotationDetailDTO quotation) {
        CustomResult rspResult = new CustomResult();

        String token = quotation.getSgsToken();
        if (StringUtils.isBlank(token)) {
            rspResult.setMsg("当前请求的用户Token无效.");
            return rspResult;
        }
        UserInfo user = tokenClient.getUser(token);
        if (user == null) {
            rspResult.setMsg("当前请求的用户Token无效.");
            return rspResult;
        }

        quotation.setModifiedBy(user.getRegionAccount());
        quotation.setModifiedDate(DateUtils.getNow());

        updateQuotationOrderInfo(quotation);     //针对 tb_general_order
        updateQuotationInfo(quotation);          //针对 tb_sl_order
        updateOrderLineInfo(quotation, user);     //保存 / 新增OrderLine 信息


        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     * Quotation保存修改Order金额信息 ---- 针对 tb_general_order
     *
     * @param quotation
     */
    private void updateQuotationOrderInfo(QuotationDetailDTO quotation) {
        orderDetailMapper.updateQuotationOrderInfo(quotation);
    }

    /**
     * Quotation保存修改 ---- 针对 tb_sl_order
     *
     * @param quotation
     */
    private void updateQuotationInfo(QuotationDetailDTO quotation) {
        orderDetailMapper.updateQuotationInfo(quotation);
    }

    /**
     * 保存 / 新增OrderLine 信息
     *
     * @param quotation
     * @param user
     */
    private void updateOrderLineInfo(QuotationDetailDTO quotation, UserInfo user) {

        if (CollectionUtils.isEmpty(quotation.getOrderLineList())) {
            return;
        }

        List<OrderLineDTO> insertList = new ArrayList<>();
        List<OrderLineDTO> updateList = new ArrayList<>();
        // List<OrderLineAciDTO> aciInsert = new ArrayList<>(); Save不保存ACI,暂时不用


        for (OrderLineDTO orderLine : quotation.getOrderLineList()) {
            orderLine.setModifiedBy(user.getRegionAccount());

            if (StringUtils.isBlank(orderLine.getVat()) && StringUtils.equals("HK", quotation.getLocationCode())) {
                orderLine.setVat("0");
            }

            if (StringUtils.isBlank(orderLine.getReportNos())) {
                orderLine.setReportNos(quotation.getQuoteReportNos());
            }

            if (StringUtils.isBlank(orderLine.getOrderLineId())) {
                orderLine.setOrderLineId(UUID.randomUUID().toString());
                orderLine.setGeneralOrderId(quotation.getGeneralOrderId());
                orderLine.setCreatedBy(user.getRegionAccount());
                insertList.add(orderLine);
            } else {
                updateList.add(orderLine);
            }
        }

        if (insertList.size() > 0) {
            int sequence = queryOrderLineSequence(quotation.getGeneralOrderId(), quotation.getQuoteReportNos());
            for (OrderLineDTO dto : insertList) {
                dto.setSequence(sequence);
                dto.setCreatedDate(DateUtils.getNow());
                dto.setModifiedDate(DateUtils.getNow());
                sequence++;
            }
            orderDetailMapper.createOrderLineList(insertList);
        }
        if (updateList.size() > 0) {
            for (OrderLineDTO dto : updateList) {
                dto.setModifiedDate(DateUtils.getNow());
            }
            orderDetailMapper.updateOrderLineList(updateList);
        }
    }

    /**
     * 查询当前序列最大值
     *
     * @param generalOrderId
     * @return
     */
    private int queryOrderLineSequence(String generalOrderId, String reportNos) {
        Map<String, String> map = new HashMap<>();
        map.put("generalOrderId", generalOrderId);
        map.put("reportNos", reportNos);

        return orderDetailMapper.queryOrderMaxSequence(map);
    }


    CustomResult dealWithProductRspList(List<ProductSampleRsp> productSampleRspList, OrderDetailDto dto) {
        CustomResult result = new CustomResult();
        for (ProductSampleRsp productSampleRsp : productSampleRspList) {
            ProductInfo product = productSampleRsp.getProduct();
            List<ProductSampleInfo> productSamples = productSampleRsp.getProductSamples();
            //处理form
            product.setId("");
            //处理grid
            if (CollectionUtils.isEmpty(productSamples)) {
                productSampleRsp.setProductSamples(Lists.newArrayList(this.createProductSampleInfo()));
            }
        }
        //多语言的洗唛，只需要其中一个语言的图标进行copy就行
        List<CareLabelInfo> careLabels = productSampleRspList.get(0).getCareLabels();
        Map<String, FileInfo> oldFileIdNewFileInfo = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(careLabels)) {
            //获取有洗唛的对象 执行洗唛图标copy
            for (CareLabelInfo careLabel : careLabels) {
                String careLabelFileId = careLabel.getCareLabelFileId();
                if (StringUtils.isBlank(careLabelFileId)) {
                    continue;
                }
                //需要copy一份洗唛图标，用作preorder
                FileInfo fileInfo = new FileInfo();
                fileInfo.setId(careLabelFileId);
                fileInfo.setOrderId(dto.getHeaders().getNewOrderId());
                fileInfo.setObjectID(dto.getHeaders().getNewOrderId());
                CustomResult customResult = fileClient.copyFile(fileInfo);
                boolean success = customResult.isSuccess();
                if (!success) {
                    result.setSuccess(false);
                    result.setMsg("Copy CareLabel file fail!");
                    return result;
                }
                FileInfo info = (FileInfo) customResult.getData();
                oldFileIdNewFileInfo.put(careLabelFileId, info);
            }
        }
        if (!oldFileIdNewFileInfo.isEmpty()) {
            outer:
            for (ProductSampleRsp productSampleRsp : productSampleRspList) {
                List<CareLabelInfo> careLabelsInfos = productSampleRsp.getCareLabels();
                inner:
                for (CareLabelInfo careLabelsInfo : careLabelsInfos) {
                    String careLabelFileId = careLabelsInfo.getCareLabelFileId();
                    if (StringUtils.isBlank(careLabelFileId)) {
                        continue inner;
                    }
                    FileInfo fileInfo = oldFileIdNewFileInfo.get(careLabelFileId);
                    careLabelsInfo.setCareLabelFileId(fileInfo.getId());
                    careLabelsInfo.setCloudId(fileInfo.getCloudID());
                }
            }
        }
        result.setSuccess(true);
        return result;
    }

    void initHeaders(OrderHeaderInfo headers) {
        //headers.setServiceLevel(1);
        headers.setTat(4);
        headers.setQuoteServiceLevel(1);
//        headers.setDateEditFlag(1);
        headers.setCaseType("Local");
    }

    CustomerInfo createCustomer() {
        CustomerInfo customerInfo = new CustomerInfo();
        customerInfo.setCustomerId("");
        customerInfo.setBuyerGroup("");
        customerInfo.setCustomerGroupId("");
        return customerInfo;
    }

    void initCustomer(CustomerInfo customerInfo) {
        if (Func.isNotEmpty(customerInfo)) {
            customerInfo.setCustomerId(StrUtil.blankToDefault(customerInfo.getCustomerId(), ""));
            customerInfo.setBuyerGroup(StrUtil.blankToDefault(customerInfo.getBuyerGroup(), ""));
            customerInfo.setCustomerGroupId(StrUtil.blankToDefault(customerInfo.getCustomerGroupId(), ""));
        }
    }

    ProductInfo createProductInfo() {
        ProductInfo productInfo = new ProductInfo();
        productInfo.setdFFFormID("");
        return productInfo;
    }

    ProductSampleInfo createProductSampleInfo() {
        ProductSampleInfo productSampleInfo = new ProductSampleInfo();
        productSampleInfo.setdFFFormID("");
        productSampleInfo.setProductItemNo("SupplierCode_1");
        return productSampleInfo;
    }

    CareLabelInfo createCareLabelInfo() {
        CareLabelInfo careLabelInfo = new CareLabelInfo();
        careLabelInfo.setProductItemNo(new ArrayList<>());
        careLabelInfo.setCareLabelSeq(1);
        careLabelInfo.setImgArray(new ArrayList<>());
        return careLabelInfo;
    }

    private Boolean checkDff(OrderDetailDto reqObject) {
        Boolean flag = true;
        String reportLanguage = reqObject.getTestRequest().getReportLanguage();
        if (reqObject.getProduct() == null && CollectionUtils.isEmpty(reqObject.getProductSamples())) {
            return flag;
        }
        if (StringUtils.equals(reportLanguage, ReportLanguage.EnglishAndChineseReport.getCode())
                || StringUtils.equals(reportLanguage, ReportLanguage.ChineseReportOnly.getCode())
                || StringUtils.equals(reportLanguage, ReportLanguage.CNToEN.getCode())) {
            Set<String> formIdSets = Sets.newHashSet();
            if (reqObject.getProduct() != null) {
                formIdSets.add(reqObject.getProduct().getdFFFormID());
            }
            if (Func.isNotEmpty(reqObject.getProductSamples())) {
                formIdSets.add(reqObject.getProductSamples().get(0).getdFFFormID());
            }
            List<DffFormAttrDTO> dffFormAttrDTOs = dffClient.getDffFormAttrByDffFormIdList(formIdSets);
            dffFormAttrDTOs = dffFormAttrDTOs.stream().filter(dff -> {
                return StringUtils.equalsIgnoreCase(LanguageType.Chinese.getCode(), dff.getLanguageCode());
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(dffFormAttrDTOs)) {
                flag = false;
            } else {
                Set<String> formIdSetsExists = dffFormAttrDTOs.stream().map(dff -> {
                    return dff.getdFFFormID();
                }).collect(Collectors.toSet());
                if (formIdSets.size() != formIdSetsExists.size()) {
                    flag = false;
                }
            }
        }
        return flag;
    }


    public CustomResult<Object> deleteProductSampleById(DeleteProductSampleReq deleteProductSampleReq) {
        String refSampleID = deleteProductSampleReq.getRefSampleID();
        String orderId = deleteProductSampleReq.getOrderId();
        logger.info("delete sampleInfo:{}-{}", refSampleID, orderId);
        CustomResult rspResult = new CustomResult();

        if (Func.isEmpty(orderId)) {
            rspResult.setSuccess(false);
            rspResult.setMsg("OrderId is null");
            return rspResult;
        }

        UserInfo user = SecurityUtil.getUser();
        List<ProductInstanceDTO> productSamples = this.productMapper.getProductInfoAllByOrderId(orderId);
        List<ProductInstanceDTO> otherSample = productSamples.stream().filter(productInstanceDTO -> {
            return StringUtils.isNotBlank(productInstanceDTO.getHeaderID()) &&
                    !StringUtils.equalsIgnoreCase(productInstanceDTO.getRefSampleID(), refSampleID);
        }).collect(Collectors.toList());
        if (otherSample.size() == 0) {
            rspResult.setSuccess(false);
            rspResult.setMsg("At least one product sample");
            return rspResult;
        }
        //addby 20210910  已有关联quotation的产品不可删除
        EnquirySampleCheckReq enquirySampleCheckReq = new EnquirySampleCheckReq();
        enquirySampleCheckReq.setSampleId(deleteProductSampleReq.getSampleID());//这里sampleID指版本的id,例如A,B,C
        enquirySampleCheckReq.setEnquiryId(orderId);
        if (iEnquiryService.sampleCheck(enquirySampleCheckReq).getData()) {
            rspResult.setSuccess(false);
            rspResult.setMsg("The sample has been assigned to quotation, please un-assign it from quoation!");
            return rspResult;
        }


        try {
            if (CollectionUtils.isEmpty(productSamples)) {
                rspResult.setSuccess(true);
                return rspResult;
            }
            BaseResponse baseResponse = notesClient.deleteSample(deleteProductSampleReq.getOrderNo(), refSampleID);
            if (baseResponse.getStatus() == 200) {
                try {

                    if (StringUtils.isNotEmpty(refSampleID)) {

                        List<ProductInstanceDTO> productSamplesDele = productSamples.stream().filter(productInstanceDTO -> {
                            return StringUtils.equalsIgnoreCase(productInstanceDTO.getRefSampleID(), refSampleID);
                        }).collect(Collectors.toList());
                        transactionTemplate.execute((trans) -> {
                            Set<String> sampleIDList = productSamplesDele.stream().map(ProductInstanceDTO::getId).collect(Collectors.toSet());
                            List<String> sampleNoList = productSamplesDele.stream().map(ProductInstanceDTO::getSampleID).collect(Collectors.toList());
                            //需要删除的careLable
                            List<CareLabelInfo> careLabelInstancePOsDelete = new ArrayList<CareLabelInfo>();
                            //需要更改 Seq的careLable
                            List<CareLabelInfo> careLabelInstancePOsUpdateSeq = new ArrayList<CareLabelInfo>();

                            List<ProductCareLabelRelInfo> productCarelabelRelationshipPOs = careLabelMapper
                                    .selectByGeneralOrderId(orderId);

                            if (CollectionUtils.isNotEmpty(productCarelabelRelationshipPOs)) {
                                //找出关联了当前sample的CareLable
                                List<ProductCareLabelRelInfo> productCarelabelRelationshipNotDeleteSample = productCarelabelRelationshipPOs.stream()
                                        .filter(productCarelabelRelationshipDto -> !sampleIDList.contains(productCarelabelRelationshipDto.getProductId()))
                                        .collect(Collectors.toList());

                                if (CollectionUtils.isNotEmpty(productCarelabelRelationshipNotDeleteSample)) {
                                    Set<String> careLabelRelNotDeleteExistIDList = productCarelabelRelationshipNotDeleteSample.stream().map(productCarelabelRelationshipPO -> productCarelabelRelationshipPO.getCareLabelId()).collect(Collectors.toSet());
                                    List<CareLabelInfo> careLabelInstancePOs = careLabelMapper.getCareLabelByOrderId(orderId);
                                    if (CollectionUtils.isNotEmpty(careLabelInstancePOs)) {
                                        careLabelInstancePOsDelete = careLabelInstancePOs.stream().filter(careLabelInstancePO -> {
                                            return !careLabelRelNotDeleteExistIDList.contains(careLabelInstancePO.getId());
                                        }).collect(Collectors.toList());

                                        careLabelInstancePOsUpdateSeq = careLabelInstancePOs.stream().filter(careLabelInstancePO -> {
                                            return careLabelRelNotDeleteExistIDList.contains(careLabelInstancePO.getId());
                                        }).collect(Collectors.toList());

                                    }
                                }
                            }

                            if (CollectionUtils.isNotEmpty(careLabelInstancePOsDelete)) {
                                Set<String> careLabelDeleteIds = careLabelInstancePOsDelete.stream().map(e -> e.getId()).collect(Collectors.toSet());
                                careLabelMapper.deleteOrderAttachmentByObjectID(careLabelDeleteIds);
                                careLabelMapper.batchDelete(careLabelDeleteIds);
                            }
                            if (CollectionUtils.isNotEmpty(careLabelInstancePOsUpdateSeq)) {
                                for (CareLabelInfo careLabelInstancePO : careLabelInstancePOsUpdateSeq) {
                                    careLabelInstancePO.setCareLabelSeq(careLabelInstancePO.getCareLabelSeq() == null ? 1 : careLabelInstancePO.getCareLabelSeq());
                                }
                                careLabelInstancePOsUpdateSeq = careLabelInstancePOsUpdateSeq.stream().sorted(Comparator.comparing(CareLabelInfo::getCareLabelSeq)).collect(Collectors.toList());

                                int careLabelSeq = 1;
                                for (CareLabelInfo careLabelInstancePO : careLabelInstancePOsUpdateSeq) {
                                    careLabelInstancePO.setCareLabelSeq(careLabelSeq);
                                    careLabelSeq++;
                                }
                                careLabelMapper.updateBatchCareLabelInstanceSeq(careLabelInstancePOsUpdateSeq);
                            }
                            for (String productInstanceID : sampleIDList) {
                                careLabelMapper.deleteProductCarelabelRelationshipByID(productInstanceID);
                            }

                            if (Func.isNotEmpty(sampleIDList)) {
                                productMapper.batchDelete(sampleIDList.stream().collect(Collectors.toSet()));
                            }


                            dffRowToColumnService.saveRowToColumn(orderId);

                            //清空对应的SampleNo的值
                            if (Func.isNotEmpty(sampleNoList)) {
                                OrderAttachmentInfoPO orderAttachmentInfoPO = new OrderAttachmentInfoPO();
                                orderAttachmentInfoPO.setSampleNo("");
                                orderAttachmentInfoPO.setModifiedDate(new Date());
                                orderAttachmentInfoPO.setModifiedBy(Func.isNotEmpty(user) ? user.getRegionAccount() : "system");
                                OrderAttachmentInfoExample example = new OrderAttachmentInfoExample();
                                example.createCriteria().andSampleNoIn(sampleNoList).andGeneralOrderIDEqualTo(orderId);
                                orderAttachmentInfoMapper.updateByExampleSelective(orderAttachmentInfoPO, example);
                            }
//                            orderAttachmentMapper.deleteByOrderAndSampleNos(orderId,new ArrayList<>(sampleNoList));
                            return 1;

                        });

                    }
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                    rspResult.setSuccess(false);
                    rspResult.setMsg("Delete sample fail!");
                    return rspResult;
                }
            } else {
                rspResult.setSuccess(false);
                rspResult.setMsg(baseResponse.getMessage());
                return rspResult;
            }
        } catch (Exception e) {
            // rspResult.setIsSuccess(false);
            // rspResult.setErrorMessage("delete sample fail,please check the delete sample interface");
            rspResult.setSuccess(false);
            rspResult.setMsg("delete sample fail,please check the delete sample interface");
            return rspResult;
        }
        rspResult.setSuccess(true);
        return rspResult;
    }

    public CustomResult queryOrderDetailByOrderNoList(List<String> orderNoList) {
        CustomResult rspResult = new CustomResult();
        if (orderNoList == null || orderNoList.size() <= 0) return rspResult;

        List<OrderDTO> orderInfoList = orderDetailMapper.queryOrderDetailByOrderNoList(orderNoList);
        orderInfoList.forEach(orderDTO -> {
            //组装TestRequest
            TestRequestInfo testRequestInfo = testRequestMapper.getTestRequestByOrderId(orderDTO.getId());
            if (testRequestInfo != null && Func.isNotEmpty(testRequestInfo)) {
                TestRequestDTO testRequestDTO = new TestRequestDTO();
                testRequestDTO.setReturnResidueSampleRemark(testRequestInfo.getReturnResidueSampleRemark());
                testRequestDTO.setReturnTestedSampleRemark(testRequestInfo.getReturnTestedSampleRemark());
                orderDTO.setTestRequest(testRequestDTO);
            }

            //组装ProductSample
            orderDTO.setProductSampleInfoList(productMapper.getProductSampleByOrderId(orderDTO.getId()));
        });

        rspResult.setSuccess(true);
        rspResult.setData(orderInfoList);
        return rspResult;
    }

    public BaseResponse<OrderGroupSearchRsp> getOrderListByGroup(String orderId) {
        BaseResponse<OrderGroupSearchRsp> response = new BaseResponse<>();
        if (Func.isEmpty(orderId)) {
            response.setMessage(ResponseCode.PARAM_MISS.getMessage());
            response.setStatus(ResponseCode.PARAM_MISS.getCode());
            return response;
        }
        OrderDetailInfo orderInfo = orderDetailMapper.getOrderDetailInfo(orderId);
        if (Func.isEmpty(orderInfo)) {
            response.setMessage(ResponseCode.PARAM_VALID_ERROR.getMessage());
            response.setStatus(ResponseCode.PARAM_VALID_ERROR.getCode());
            return response;
        }
        String groupId = orderInfo.getGroupID();
        if (Func.isEmpty(groupId)) {
            return response;
        }
        List<OrderDetailInfo> orderLists = orderDetailMapper.getOrderListByGroup(groupId);
        if (Func.isNotEmpty(orderLists)) {
            List<String> cloneOrderIds = new ArrayList<>();
            List<String> orderIds = orderLists.stream().map(OrderDetailInfo::getOrderId).collect(Collectors.toList());
            OrderOperationHistoryExample example = new OrderOperationHistoryExample();
            example.createCriteria().andGeneralOrderIDIn(orderIds).andOperationTypeEqualTo(OperationTypeEnums.BatchClone.getValue());
            List<OrderOperationHistoryPO> orderOperationHistoryPOS = orderOperationHistoryMapper.selectByExample(example);
            if (Func.isNotEmpty(orderOperationHistoryPOS)){
                cloneOrderIds.addAll(orderOperationHistoryPOS.stream().map(OrderOperationHistoryPO::getGeneralOrderID).collect(Collectors.toList()));
            }
            OrderGroupSearchRsp orderGroupSearchRsp = new OrderGroupSearchRsp();
            List<OrderDetailRsp> orderList = Lists.newArrayList();
            orderLists.stream().forEach(order -> {
                OrderDetailRsp orderDetail = new OrderDetailRsp();
                orderDetail.setOrderId(order.getOrderId());
                orderDetail.setOrderNo(order.getOrderNo());
                orderDetail.setEnquiryNo(order.getEnquiryNo());
                orderDetail.setOrderStatus(order.getOrderStatus());
                orderDetail.setCloneStatus(cloneOrderIds.contains(order.getOrderId()));
                orderList.add(orderDetail);
            });
            OrderDetailRsp preOrder = null;
            OrderDetailRsp nextOrder = null;
            orderList.sort(Comparator.comparing(OrderDetailRsp::getOrderNo));
            int index = 0;
            int size = orderList.size();
            for (OrderDetailRsp order : orderList) {
                if (order.getOrderId().equals(orderId)) {
                    if (index + 1 < size) {
                        nextOrder = orderList.get(index + 1);
                    }
                    break;
                }
                preOrder = order;
                index++;
            }
            orderGroupSearchRsp.setPreOrder(preOrder);
            orderGroupSearchRsp.setNextOrder(nextOrder);
            orderGroupSearchRsp.setOrderList(orderList);
            response.setData(orderGroupSearchRsp);
        }
        return response;
    }

    /**
     * ConfirmOrder业务校验
     * 订单保存也会执行
     *
     * @return
     */
    public CustomResult<OrderDetailInfo> checkBaseField(String orderId) {
        CustomResult response = new CustomResult(true);
        OrderDetailInfo orderInfo = orderDetailMapper.getOrderDetailInfo(orderId);
        if (Func.isEmpty(orderInfo)) {
            response.setMsg("orderId 不合法");
            response.setStatus(500);
            response.fail();
            return response;
        }
        SlOrderInfoPO slOrder = orderInfo.getSlOrder();
        if (Func.isEmpty(slOrder)) {
            response.setMsg("slOrder 查询失败");
            response.setStatus(500);
            response.fail();
            return response;
        }
        String orderType = slOrder.getCaseType();
        // OrderType = Local, Applicant,Payer 校验必填 且校验是BOSS customer Buyer不校验必填，如果有值校验必须是Boss customer
        if (Func.isNotEmpty(orderType) && orderType.equals("Local")) {
            List<CustomerInfo> customers = orderInfo.getCustomers();
            if (Func.isEmpty(customers)) {
                response.setMsg("applicant,payer is required!");
                response.setStatus(500);
                response.fail();
                return response;
            }
            // Applicant
            CustomerInfo applicant = customers.stream().filter(customer -> customer.getCustomerUsage().equals(CustomerUsage.Applicant.getCode())).findFirst().orElse(null);
            if (Func.isEmpty(applicant)) {
                response.setMsg("applicant is required!");
                response.setStatus(500);
                response.fail();
                return response;
            }
            // Payer
            CustomerInfo payer = customers.stream().filter(customer -> customer.getCustomerUsage().equals(CustomerUsage.Payer.getCode())).findFirst().orElse(null);
            if (Func.isEmpty(payer)) {
                response.setMsg("payer is required!");
                response.setStatus(500);
                response.fail();
                return response;
            }
            //根据BuParam配置判断是否校验BossCustomer
            BuParamReq buParamReq = new BuParamReq();
            buParamReq.setGroupCode("customer");
            buParamReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            buParamReq.setParamCode("BossCustomerVerifyWhenOrderConfirm");
            List<BuParamValueRsp> bossCustomerVerifyBuParams = frameWorkClient.getBuParams(buParamReq);
            Boolean bossCustomerVerifyWhenOrderConfirm = false;
            if (Func.isNotEmpty(bossCustomerVerifyBuParams)) {
                BuParamValueRsp buParamValueRsp = bossCustomerVerifyBuParams.get(0);
                if (Func.isNotEmpty(buParamValueRsp) && Func.equals(buParamValueRsp.getParamValue(), "Y")) {
                    bossCustomerVerifyWhenOrderConfirm = true;
                }
            }
            if (Func.isNotEmpty(bossCustomerVerifyWhenOrderConfirm) && bossCustomerVerifyWhenOrderConfirm) {
                //Verify Applicant Boss Customer
                Long applicantBossNumber = applicant.getBossNumber();
                if (Func.isEmpty(applicantBossNumber)) {
                    response.setMsg("Please select a Boss customer as applicant!");
                    response.setStatus(500);
                    response.fail();
                    return response;
                }
                //Verify Payer Boss Customer
                Long payerBossNumber = payer.getBossNumber();
                if (Func.isEmpty(payerBossNumber)) {
                    response.setMsg("Please select a Boss customer as payer!");
                    response.setStatus(500);
                    response.fail();
                    return response;
                }
                //Verify Buyer Boss Customer
                CustomerInfo buyer = customers.stream().filter(customer -> customer.getCustomerUsage().equals(CustomerUsage.Buyer.getCode())).findFirst().orElse(null);
                if (Func.isNotEmpty(buyer)) {
                    Long buyerBossNumber = buyer.getBossNumber();
                    if (Func.isEmpty(buyerBossNumber)) {
                        response.setMsg("Please select a Boss customer as buyer!");
                        response.setStatus(500);
                        response.fail();
                        return response;
                    }
                }
            }
        }
        response.setData(orderInfo);
        return response;
    }

    public BaseResponse checkReportTemplate(ConfirmOrderReq confirmOrderReq) {
        BaseResponse response = new BaseResponse();
        TestRequestInfo testRequestInfo = testRequestMapper.getTestRequestByOrderNo(confirmOrderReq.getOrderNo());
        if (Func.isNotEmpty(testRequestInfo) && Func.isNotEmpty(testRequestInfo.getReportLanguage())) {
            ReportTemplateSettingReq req = new ReportTemplateSettingReq();
            req.setOrderNo(confirmOrderReq.getOrderNo());
            req.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            req.setLanguageID(Integer.valueOf(testRequestInfo.getReportLanguage()));
            logger.info("checkReportTemplateList Req :{}", JSON.toJSONString(req));
            BaseResponse<Boolean> checkResult = reportFacade.checkReportTemplateList(req);
            logger.info("checkReportTemplateList Res :{}", JSON.toJSONString(checkResult));
            if (Func.isEmpty(checkResult) || Func.isEmpty(checkResult.getData()) || !checkResult.getData()) {
                response.setMessage("未检索到与Report Language要求相匹配的Report Template，可能影响后续的报告生成。是否继续当前操作？");
                response.setStatus(500);
                return response;
            }
        }
        return response;
    }

    public CustomResult<OrderDetailInfo> checkConfirmOrder(ConfirmOrderReq confirmOrderReq) {
        String orderId = confirmOrderReq.getOrderId();
        CustomResult response = new CustomResult(true);
        // 前台
        CustomResult<OrderDetailInfo> checkRes = this.checkBaseField(confirmOrderReq.getOrderId());
        if (!checkRes.isSuccess()) {
            return checkRes;
        }
        OrderDetailInfo orderInfo = checkRes.getData();
        if (Func.isEmpty(orderInfo)) {
            response.setMsg("orderId 不合法");
            response.setStatus(500);
            response.fail();
            return response;
        }
        // charge Order 不校验 Standard Full Name
        //校验TestLine的Standard是否为客户指定，且有值
        //查询Order Report Language
        if(!OperationModeEnum.check(orderInfo.getOperationMode(), OperationModeEnum.ChargeOnly)){
            TestRequestInfo testRequestInfo = testRequestMapper.getTestRequestByOrderId(confirmOrderReq.getOrderId());
            if(Func.isEmpty(testRequestInfo) || Func.isEmpty(testRequestInfo.getReportLanguage())){
                response.setMsg("查询Report Language 失败，请检查订单信息");
                response.setStatus(500);
                response.fail();
                return response;
            }
            CheckTLStandFullNameReq checkTLStandFullNameReq = new CheckTLStandFullNameReq();
            checkTLStandFullNameReq.setOrderNo(orderInfo.getOrderNo());
            checkTLStandFullNameReq.setReportLanguage(testRequestInfo.getReportLanguage());
            checkTLStandFullNameReq.setProductLineCode(confirmOrderReq.getProductLineCode());
            BaseResponse checkTLRsp = testLineFacade.checkTLStandFullName(checkTLStandFullNameReq);
            if(checkTLRsp.isFail()){
                response.setMsg(checkTLRsp.getMessage());
                response.setStatus(checkTLRsp.getStatus());
                response.fail();
                return response;
            }
        }
        if (confirmOrderReq.isExecuteQuotationMatrix()) {
            // 校验Sample是否使用，Sample都被Assign 到了Service Item 校验QuotationType必须包含Test项目
            ConfirmOrderRequest confirmOrderRequest = new ConfirmOrderRequest();
            confirmOrderRequest.setOrderId(orderId);
            confirmOrderRequest.setEnquiryFlag(0);
            confirmOrderRequest.setProductLineCode(confirmOrderReq.getProductLineCode());
            confirmOrderRequest.setSystemId(SgsSystem.GPO.getSgsSystemId());
            logger.info("confirmOrderRequest:{}", JSON.toJSONString(confirmOrderRequest));
            BaseResponse<Boolean> checkResult = quotationMatrixFacade.confirmMatrix(confirmOrderRequest);
            if (Func.isEmpty(checkResult)) {
                response.setMsg("调用confirmMatrix失败");
                response.setStatus(500);
                response.fail();
                return response;
            }
            if (Func.isEmpty(checkResult.getData()) || !checkResult.getData()) {
                response.setMsg(Func.isNotEmpty(checkResult.getMessage()) ? checkResult.getMessage() : "校验 Sample , Service Item Assign 不通过，请检查数据");
                response.setStatus(500);
                response.fail();
                return response;
            }

        }
        //OrderType = Local Applicant,Payer 校验必填 且校验是BOSS customer Buyer不校验必填，如果有值校验必须是Boss customer
        // OrderType = IDB,所有角色不校验必填
        // 基于ObjectSetting的必填项校验
        CheckRequiredFieldReq checkReq = new CheckRequiredFieldReq();
        checkReq.setObject("order");
        checkReq.setObjectId(orderId);
        checkReq.setProductLineCode(orderInfo.getBuCode());
        checkReq.setToken(confirmOrderReq.getToken());
        checkReq.setCaseType(orderInfo.getSlOrder().getCaseType());
        checkReq.setOperationMode(orderInfo.getOperationMode());
        try {
            CustomResult<CheckRequiredFieldRsp> checkRequiredResult = checkRequiredField(checkReq);
            if (Func.isNotEmpty(checkRequiredResult.getData()) && Func.isNotEmpty(checkRequiredResult.getData().getErrorMessage())) {
                response.setMsg(checkRequiredResult.getMsg());
                response.setStatus(500);
                response.fail();
                return response;
            }
        } catch (IllegalAccessException e) {
            logger.info("confirmOrder e:{}", e.getMessage());
        }
        //第一次Confirm Order 需要处理的业务逻辑
        int orderStatus = orderInfo.getOrderStatus();
        // 判断是否是第一次执行
//        boolean firstTime = (OrderStatus.checkStatus(orderStatus, OrderStatus.New));
//        Date orderConfirmDate = new Date();
//        if (firstTime) {
//            // Confirm Order的时候校验当前时间不能晚于Order DueDate
//            Date orderDueDate = orderInfo.getExpectedOrderDueDate();
//            if (Func.isEmpty(orderDueDate)) {
//                response.setMessage("Order Expect DueDate can`t be empty！");
//                response.setStatus(500);
//                return response;
//            }
//            SimpleDateFormat fmt = new SimpleDateFormat("yyyyMMdd");
//            if (Integer.valueOf(fmt.format(orderConfirmDate)) > Integer.valueOf(fmt.format(orderDueDate))) {
//                response.setMessage("Order expect due date cannot be earlier than Order confirm date！");
//                response.setStatus(500);
//                return response;
//            }
//        }
        // 判断数据版本,如果数据已经被更新了，需要返回错误
        if(Func.isNotEmpty(confirmOrderReq.getVersionId())){
            OrderVersionDTO orderVersion = new OrderVersionDTO();
            orderVersion.setOrderId(orderId);
            orderVersion.setVersionId(confirmOrderReq.getVersionId());
            int versionCheck = orderMapper.updateVersionId(orderVersion);
            if (versionCheck == 0) {
                response.setMsg("订单部分内容刚刚被其他人修改了，请刷新页面重新获取最新内容后再进行更新。");
                response.setStatus(500);
                response.fail();
                return response;
            }
        }
        response.setData(checkRes.getData());
        response.setSuccess(true);
        return response;
    }

    /**
     * 同时满足条件
     * 1 自动更新打开
     *
     * @param orderInfo
     * @return
     */
    private boolean needUpdateDueDate(OrderDetailInfo orderInfo) {
        return DateEditFlagEnum.check(orderInfo.getSlOrder().getDateEditFlag(), DateEditFlagEnum.ON);
    }

    @Retryable(scene = "OrderDetailService-confirmOrder",async = false, retryStrategy = RetryType.LOCAL_REMOTE,include ={ RetryException.class,RuntimeException.class})
    public CustomResult confirmOrder(ConfirmOrderReq confirmOrderReq) {
        CustomResult response = new CustomResult(true);
        String token = Func.isNotEmpty(confirmOrderReq.getToken())?confirmOrderReq.getToken():tokenClient.getToken();
        UserInfo userInfo = Func.isNotEmpty(confirmOrderReq.getUserInfo())?confirmOrderReq.getUserInfo():tokenClient.getUser();

        if (Func.isEmpty(token) || Func.isEmpty(userInfo)) {
            response.setMsg("token 已失效");
            response.setStatus(500);
            response.fail();
            return response;
        }
        String orderId = confirmOrderReq.getOrderId();
        if (Func.isEmpty(orderId)) {
            response.setMsg("orderId 不能为空");
            response.setStatus(500);
            response.fail();
            return response;
        }
        OrderInfoDto oldOrderDTO = orderMapper.getOrderInfo(orderId);
        Date oldExpectedOrderDueDate = null;
        if (Func.isNotEmpty(oldOrderDTO)) {
            oldExpectedOrderDueDate = oldOrderDTO.getExpectedOrderDueDate();
        }
        // 如果有订单信息需要自动执行保存
        if (Func.isNotEmpty(confirmOrderReq.getOrder())) {
            OrderDetailDto reqObject = confirmOrderReq.getOrder();
            CustomResult<OrderDetailRsp> rspResult = this.saveOrderInfo(reqObject);
            if (!rspResult.isSuccess()) {
                response.setMsg(rspResult.getMsg());
                response.setStatus(500);
                response.fail();
                return response;
            }
        }
        // 订单Confirm需要执行的校验
        CustomResult<OrderDetailInfo> checkRes = this.checkConfirmOrder(confirmOrderReq);
        if (!checkRes.isSuccess()) {
            return checkRes;
        }
        OrderDetailInfo orderInfo = checkRes.getData();
        int orderStatus = orderInfo.getOrderStatus();
        boolean firstTime = (OrderStatus.checkStatus(orderStatus, OrderStatus.New));
        Date orderConfirmDate = new Date();

        // ConfirmOrder时会计算当下准确的DueDate
        // 需要记录的BizLog
        List<BizLogInfo> newBizLogs = Lists.newArrayList();
        // Confirm Order 订单信息准备
        // 待更新的订单信息
        OrderInfoPO newOrderInfo = null;
        SlOrderInfoPO slOrderInfoPO = null;
        List<String> orderNos = Lists.newArrayList();
        orderNos.add(orderInfo.getOrderNo());
        // 补偿发送
        List<ProcessRecordForReportDTO> processRecords = Lists.newArrayList();
        if (firstTime) {
            newOrderInfo = new OrderInfoPO();
            newOrderInfo.setID(orderId);
            // 更新订单状态为Confirm
            newOrderInfo.setOrderStatus(OrderStatus.Confirmed.getStatus());
            newOrderInfo.setOrderConfirmDate(orderConfirmDate);
            //GPO2-11708 ConfirmOrder时设置Actual Start Date
            newOrderInfo.setServiceStartDate(orderConfirmDate);
            slOrderInfoPO = new SlOrderInfoPO();
            slOrderInfoPO.setSampleConfirmDate(orderConfirmDate);
            slOrderInfoPO.setOrderId(orderId);
            // 第一次Confirm 如果开关打开需要重新计算DudDate
            if(needUpdateDueDate(orderInfo)){
                orderInfo.setServiceStartDate(orderConfirmDate);
                BaseResponse<Date> orderDueDateRes = this.calExpectDueDate(orderInfo);
                logger.info("confirmOrderDueDateRes:{}",orderDueDateRes);
                if(orderDueDateRes.isSuccess() && Func.isNotEmpty(orderDueDateRes.getData())){
                    newOrderInfo.setExpectedOrderDueDate(orderDueDateRes.getData());
                    orderInfo.setExpectedOrderDueDate(orderDueDateRes.getData());
                }
            }
            // Confirm Order的时候校验当前时间不能晚于Order DueDate
            Date orderDueDate = orderInfo.getExpectedOrderDueDate();
            if (Func.isEmpty(orderDueDate)) {
                response.setMsg("Order Expect DueDate can`t be empty！");
                response.setStatus(500);
                return response;
            }
            SimpleDateFormat fmt = new SimpleDateFormat("yyyyMMdd");
            if (Integer.valueOf(fmt.format(orderConfirmDate)) > Integer.valueOf(fmt.format(orderDueDate))) {
                response.setMsg("Order expect due date cannot be earlier than Order confirm date！");
                response.setStatus(500);
                return response;
            }
            // 记录订单更新日志
            BizLogInfo bizLog = new BizLogInfo();
            bizLog.setBizId(orderInfo.getOrderNo());
            bizLog.setBu(orderInfo.getBuCode());
            bizLog.setLab(orderInfo.getLocationCode());
            bizLog.setBizOpType(BizLogConstant.ORDER_OPERATION_HISTORY);
            bizLog.setOpType("Confirm Order");
            bizLog.setOpUser(userInfo.getRegionAccount());
            bizLog.setNewVal(Func.isEmpty(confirmOrderReq.getBizLogCloneFlag()) ? "" : confirmOrderReq.getBizLogCloneFlag());
            newBizLogs.add(bizLog);
            // 记录状态变更日志
            BizLogInfo statusBizLog = new BizLogInfo();
            statusBizLog.setBu(orderInfo.getBuCode());
            statusBizLog.setLab(orderInfo.getLocationCode());
            statusBizLog.setOpUser(userInfo.getRegionAccount());
            statusBizLog.setBizId(orderInfo.getOrderNo());
            statusBizLog.setBizOpType(BizLogConstant.ORDER_STATUS_CHANGE_HISTORY);
            statusBizLog.setOpType(Constants.BIZ_LOG.OP_TYPE.ORDER_CONFIRM);
            statusBizLog.setNewVal(OrderStatus.Confirmed.getStatus());
            statusBizLog.setOriginalVal(orderStatus);
            newBizLogs.add(statusBizLog);
        } else {
            processRecords = trackingClient.getProcessRecordForReport(orderNos, confirmOrderReq.getProductLineCode(), TrackingPointEnum.ConfirmMatrix.getPointCode());
            if(Func.isEmpty(orderInfo.getOrderConfirmDate())){
                newOrderInfo = new OrderInfoPO();
                newOrderInfo.setID(orderId);
                if(Func.isNotEmpty(processRecords)){
                    ProcessRecordForReportDTO lastRecord = processRecords.stream().filter(item -> Func.isNotEmpty(item.getCreatedDate())).sorted(Comparator.comparing(ProcessRecordForReportDTO::getCreatedDate).reversed()).findFirst().orElse(null);
                    //orderConfirmDate没值但已经发送过tracking的，应该将tracking记录的日期作为OrderConfirmDate
                    if(Func.isNotEmpty(lastRecord)){
                        orderConfirmDate = lastRecord.getCreatedDate();
                    }
                }
                newOrderInfo.setOrderConfirmDate(orderConfirmDate);
            }
        }

        // GPO处理DB更新
        // 更新DB order状态
        OrderInfoPO finalNewOrderInfo = newOrderInfo;
        SlOrderInfoPO finalSlOrderInfoPO = slOrderInfoPO;
        long costTime = System.currentTimeMillis();
        logger.info("costTime:{}", costTime);
        syncInfoToExtSystem(confirmOrderReq, orderInfo);
        logger.info("costTime:{}", System.currentTimeMillis() - costTime);
        transactionTemplate.execute(transaction -> {
            if (Func.isNotEmpty(finalNewOrderInfo)) {
                orderInfoMapper.updateByPrimaryKeySelective(finalNewOrderInfo);
            }
            if(Func.isNotEmpty(finalSlOrderInfoPO) && Func.isNotEmpty(finalSlOrderInfoPO.getSampleConfirmDate())){
                slOrderMapper.updateSampleConfirmDate(finalSlOrderInfoPO);
            }
            if (Func.equals(OperationModeEnum.ChargeOnly.getOperationMode(), orderInfo.getOperationMode())) {//chargeorder,触发关单
                CloseOrderRequest closeReq = CloseOrderRequest.builder().orderNo(orderInfo.getOrderNo()).lastReport(false).build();
                closeReq.setToken(token);
                orderService.closeOrder(closeReq, false);
            }
            return 1;
        });
        if (!response.isSuccess()) {
            return response;
        }

        // 外部系统逻辑处理
        if (firstTime) {
            ConfirmOrderReq trfReq = new ConfirmOrderReq();
            trfReq.setOrderId(confirmOrderReq.getOrderId());
            trfReq.setToken(confirmOrderReq.getToken());
            trfReq.setProductLineCode(confirmOrderReq.getProductLineCode());
            BaseResponse trfIfNecessaryBaseResponse = createTrfIfNecessary(trfReq);
            logger.info("Confirm Order createTrf:{}", trfIfNecessaryBaseResponse);
            if (!trfIfNecessaryBaseResponse.isSuccess()) {
                response.setMsg(trfIfNecessaryBaseResponse.getMessage());
                response.setStatus(trfIfNecessaryBaseResponse.getStatus());
                return response;
            }
        }
        // 记录tracking
        if (Func.isEmpty(processRecords)) {
            trackingService.sendConfirmMatrix(orderInfo.getOrderNo(), token, userInfo.getRegionAccount());
        }
        if (Func.isNotEmpty(newBizLogs)) {
            newBizLogs.stream().forEach(bizLogInfo -> {
                bizLogClient.doSend(bizLogInfo);
            });
        }
        LabInfoPO labInfoPO = this.getLabInfo(orderInfo.getOrderId());

        PreEvent preEvent = new PreEvent();
        preEvent.setEventSource(StandardObjectType.Order.getName());
        preEvent.setEventType(EventType.Confirmed.getTypeName());
        preEvent.setEventSourceStatus(OrderStatus.Confirmed.getStatus());
        preEvent.setToken(token);
        preEvent.setEventSourceNo(confirmOrderReq.getOrderNo());
        preEvent.setEventSourceId(confirmOrderReq.getOrderId());
        preEvent.setOrderId(confirmOrderReq.getOrderId());
        preEvent.setOrderNo(confirmOrderReq.getOrderNo());
        preEvent.setProductLineCode(orderInfo.getBuCode());
//        eventCenterService.processEvent(preEvent);
        scheduleTaskClient.sendEventMessage(preEvent, labInfoPO.getLabCode());
        //更新OrderDueDateConfirmFlag
        try {
            OrderDueDateConfirmReq orderDueDateConfirmReq = new OrderDueDateConfirmReq();
            orderDueDateConfirmReq.setTriggerAction(Constants.BU_PARAM.DUE_DATE.ORDER_DUE_DATE_CONFIRM_TRIGGER.VALUES.ORDER_CONFIRM);
            orderDueDateConfirmReq.setOldOrderDueDate(oldExpectedOrderDueDate);
            orderDueDateConfirmReq.setOrderId(orderId);
            BaseResponse<Integer> updateOrderDueDateConfirmFlagRsp = this.orderDueDateConfirm(orderDueDateConfirmReq);
        } catch (Exception e) {
            logger.error("confirm Order updateOrderDueDateConfirmFlag err:{}", e);
        }
        return response;
    }

    private void syncInfoToExtSystem(ConfirmOrderReq confirmOrderReq, OrderDetailInfo orderInfo) {
        // TODO 因为ToTest的方法中有更新gpo库的动作，如果后执行会出现死锁
        if (!Func.equals(OperationModeEnum.ChargeOnly.getOperationMode(), orderInfo.getOperationMode())) {
            // 最后执行To Test
            sendToTestResult(confirmOrderReq, orderInfo);

            //生成matrixNo
            generateMatrixNoResponse(orderInfo);

            // matrix confirm date
            updateMatrixConfirmDateBaseResponse(orderInfo);
        }
    }

    private BaseResponse updateMatrixConfirmDateBaseResponse(OrderDetailInfo orderInfo) throws RetryException{
        ToTestReq toTestReq = new ToTestReq();
        toTestReq.setOrderNo(orderInfo.getOrderNo());
        toTestReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse updateMatrixConfirmDateBaseResponse = testMatrixFacade.updateMatrixConfirmDate(toTestReq);
        if(updateMatrixConfirmDateBaseResponse.isFail()){
            throw new RetryException(500, updateMatrixConfirmDateBaseResponse.getMessage());
        }
        return updateMatrixConfirmDateBaseResponse;
    }

    private void generateMatrixNoResponse(OrderDetailInfo orderInfo) throws RetryException {
        GenerateMatrixNoReq generateMatrixNoReq = new GenerateMatrixNoReq();
        generateMatrixNoReq.setOrderNo(orderInfo.getOrderNo());
        generateMatrixNoReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse generateMatrixNoResponse = testMatrixFacade.generateMatrixNo(generateMatrixNoReq);
        String message = null;
        if (Func.isEmpty(generateMatrixNoResponse)) {
            message = "生成testMatrixNo失败";
        } else if (generateMatrixNoResponse.isFail()) {
           message = generateMatrixNoResponse.getMessage();
        }
        if(Func.isNotEmpty(message)){
            throw new RetryException(500, message);
        }
    }

    private void sendToTestResult(ConfirmOrderReq confirmOrderReq, OrderDetailInfo orderInfo) throws RetryException {
        PriceEngineRequest toTestRequest = new PriceEngineRequest();
        toTestRequest.setOrderId(orderInfo.getOrderId());
        toTestRequest.setBuCode(orderInfo.getBuCode());
        toTestRequest.setBuId(orderInfo.getBuId().toString());
        toTestRequest.setLocationCode(orderInfo.getLocationCode());
        toTestRequest.setLabCode(orderInfo.getLab().getLabCode());
        toTestRequest.setSgsToken(confirmOrderReq.getToken());
        toTestRequest.setProductLineCode(orderInfo.getBuCode());
        toTestRequest.setSystemId(SgsSystem.GPO.getSgsSystemId());
        toTestRequest.setAssignAllLabSection(confirmOrderReq.isAssignAllLabSection());
        toTestRequest.setOldOrderId(confirmOrderReq.getOldOrderId());
        BaseResponse toTestResult = quotationFacade.toTest(toTestRequest);
        logger.info("ToTestResult:,{}", JSON.toJSONString(toTestResult));
        //TODO Test
        if (toTestResult.isFail()||confirmOrderReq.getVersionId()==0) {
            throw new RetryException(500, toTestResult.getMessage());
        }
    }

    /**
     * 基于最新的TAT的值计算
     *
     * @param orderInfo
     * @param
     * @return
     */
    private BaseResponse<Date> calExpectDueDate(OrderDetailInfo orderInfo) {
        BaseResponse<Date> calRes = new BaseResponse<>();
        CalExpectDueDateReq request = new CalExpectDueDateReq();
        request.setBuId(orderInfo.getBuId().toString());
        request.setLocationId(orderInfo.getLocationId().toString());
        request.setLabCode(orderInfo.getLab().getLabCode());
        request.setExpectDueDateType(ExpectDueDateType.Order.getCode());
        request.setServiceType(String.valueOf(orderInfo.getServiceLevel()));
        Date serviceStartDate = Func.isEmpty(orderInfo.getServiceStartDate()) ? new Date() : orderInfo.getServiceStartDate();
        request.setOrderConfirmDate(serviceStartDate);
        String tatSampleReceiveTime = DateUtils.format(serviceStartDate, "HH:mm:ss");
        request.setTatDate(orderInfo.getTat());
        request.setTatSampleReceiveTime(tatSampleReceiveTime);
        List<SampleReceiveTimeInfo> expectDueDateList = expectDueDateService.calExpectDueDate(request);
        if (Func.isEmpty(expectDueDateList)) {
            return calRes;
        }
        SampleReceiveTimeInfo orderDueDateInfo = expectDueDateList.stream()
                .filter(expectDueDate -> Func.equalsSafe(expectDueDate.getExpectDueDateType(), ExpectDueDateType.Order.getCode()))
                .findAny().orElse(null);
        if (Func.isNotEmpty(orderDueDateInfo)) {
            calRes.setData(DateUtils.parseDate(orderDueDateInfo.getResultDate()));
            return calRes;
        }
        return calRes;
    }

    public BaseResponse createTrfIfNecessary(ConfirmOrderReq confirmOrderReq) {
        BaseResponse baseResponse = new BaseResponse();
        String orderId = confirmOrderReq.getOrderId();
        int orderTrfCount = orderTrfRelationshipMapper.countTrfRelationshipByOrderId(orderId);
        if (orderTrfCount > 0) {
            logger.info("createTrf ，trf已存在，不需要创建orderTrfCount={} : orderId={} ", orderId, orderTrfCount);
            return baseResponse;
        }
        String sgsToken = tokenClient.getToken();
        if (Func.isEmpty(sgsToken)) {
            sgsToken = confirmOrderReq.getToken();
        }
        UserInfo userInfo = tokenClient.getUser(sgsToken);

        OrderDetailDto orderDetailDto = confirmOrderReq.getOrder();
        if (Func.isEmpty(orderDetailDto)) {
            CustomResult customResult = this.getOrderDetailInfo(confirmOrderReq.getOrderId(), sgsToken);
            orderDetailDto = (OrderDetailDto) customResult.getData();
        }
        if (Func.isEmpty(orderDetailDto)) {
            return baseResponse;
        }
        OrderHeaderInfo headers = orderDetailDto.getHeaders();
        logger.info("createTrf request :orderNo={} ", headers.getOrderNo());
        if(Func.isEmpty(headers.getToDMFlag()) || !(headers.getToDMFlag().equals(1))){
            return baseResponse;
        }
        boolean order2TRF = canOrder2TRF(orderDetailDto);
        if (!order2TRF) {
            logger.info("order2TRF判断，applicant和buyer的order2trf标记未打开，不需要自动order2trf，orderNo={} ", headers.getOrderNo());
            //手动toSGSMart时 这个校验返回Message为flag，表示标记未打开
            baseResponse.setMessage("flag");
            return baseResponse;
        }


        if (Func.isEmpty(orderDetailDto.getProductSampleRspList())) {
            CustomResult<OrderProductRsp> productSampleList = this.getProductSampleList(confirmOrderReq.getOrderId());
            List<ProductSampleRsp> productSampleRspList = productSampleList.getData().getProductSampleRspList();
            orderDetailDto.setProductSampleRspList(productSampleRspList);
        }
        if (Func.isEmpty(orderDetailDto.getHeaders().getCaseType())
                || CaseType.check(orderDetailDto.getHeaders().getCaseType(), CaseType.IDB)) {
            return baseResponse;
        }
        //非Full Cycle 不允许To SGSMart
        if (Func.isEmpty(orderDetailDto.getOperationMode()) || !OperationModeEnum.check(orderDetailDto.getOperationMode(), OperationModeEnum.FullCycle)) {
            return baseResponse;
        }
        //非PDF不允许To SGSMart
        if (Func.isEmpty(orderDetailDto.getTestRequest().getReportRequirement())
                || !ReportRequirementEnum.check(orderDetailDto.getTestRequest().getReportRequirement(), ReportRequirementEnum.Customer_Report_PDF)) {
            return baseResponse;
        }

        //调用新工程创建TRF
        GpoSciOrderToTrfReq gpoSciOrderToTrfReq = new GpoSciOrderToTrfReq();
        gpoSciOrderToTrfReq.setRefSystemId(RefSystemIdEnum.SGSMart.getRefSystemId());
        gpoSciOrderToTrfReq.setOrderNo(headers.getOrderNo());
        gpoSciOrderToTrfReq.setToken(sgsToken);
        gpoSciOrderToTrfReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<Order2TrfRsp> trfResponse = sciFacade.order2Trf(gpoSciOrderToTrfReq);
        if(trfResponse.isFail()){
            return BaseResponse.newFailInstance("创建Trf失败:"+trfResponse.getMessage());
        }
        Order2TrfRsp order2TrfRsp = trfResponse.getData();
        if (Func.isEmpty(order2TrfRsp) || Func.isEmpty(order2TrfRsp.getTrfNo())) {
            return BaseResponse.newFailInstance("创建Trf失败，没获取到trfNo");
        }
        String trfNo = order2TrfRsp.getTrfNo();
        Integer refSystemId = order2TrfRsp.getRefSystemId();


       /* BaseResponse<String> trfResponse = ilayerClient.createTrf(orderDetailDto);
        if(!trfResponse.isSuccess()){
            baseResponse = BaseResponse.newFailInstance(trfResponse.getMessage());
            return baseResponse;
        }
        String trfNo = trfResponse.getData();*/
        OrderTrfRelInfo trf = new OrderTrfRelInfo();
        trf.setOrderId(headers.getOrderId());
        trf.setRefNo(trfNo);
        trf.setRefSystemId(refSystemId);
        trf.setRefObjectType(RefObjectTypeEnum.Order.getType());
        trf.setRefObjectId(headers.getOrderId());
        trf.setCreatedBy(userInfo.getRegionAccount());
        trf.setCreatedDate(DateUtils.getNow());
        trf.setModifiedBy(userInfo.getRegionAccount());
        trf.setModifiedDate(DateUtils.getNow());
        trf.setTrfSourceType(TrfSourceType.Order2TRF.getSourceType());
        trf.setIntegrationChannel(RefIntegrationChannel.SCI.getCode());
        orderTrfRelationshipMapper.saveOrderTrfRelationshipBatch(Arrays.asList(trf));

        BizLogInfo bizLog = new BizLogInfo();
        bizLog.setBizId(headers.getOrderNo());
        bizLog.setBu(headers.getBuCode());
        bizLog.setLab(headers.getLocationCode());
        bizLog.setOpUser(SystemContextHolder.getRegionAccount());
        bizLog.setOpType("OrderToTRF");
        bizLog.setBizOpType(BizLogConstant.ORDER_OPERATION_HISTORY);
        bizLog.setNewVal("TRFNo." + trfNo);
        bizLogClient.doSend(bizLog);
        return trfResponse;
    }

    private boolean canOrder2TRF(OrderDetailDto orderDetailDto) {
        OrderHeaderInfo headers = orderDetailDto.getHeaders();

        List<CustomerInfo> customerList = new ArrayList<>();
        if (Func.isNotEmpty(orderDetailDto.getApplicant()) && Func.isNotEmpty(orderDetailDto.getApplicant().getCustomerId())) {
            customerList.add(orderDetailDto.getApplicant());
        }
        if (Func.isNotEmpty(orderDetailDto.getBuyer()) && Func.isNotEmpty(orderDetailDto.getBuyer().getCustomerId())) {
            customerList.add(orderDetailDto.getBuyer());
        }
        for (CustomerInfo customerInfo : customerList) {
            List<CustomerExtDTO> customerExtDTOS = customerClient.getCustomerExt(customerInfo.getCustomerId(), headers.getLocationCode(), headers.getBuCode());
            if (Func.isEmpty(customerExtDTOS)) {
                continue;
            }
            for (CustomerExtDTO customerExtDTO : customerExtDTOS) {
                logger.info("check customer order2trf flag={},orderNo={},customerUsage={}", customerExtDTO.getOrderToTRFFlag(), headers.getOrderNo(), customerInfo.getCustomerUsage());
                if (Func.equalsSafe(1, customerExtDTO.getOrderToTRFFlag())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 校验配置项中的必填项是不是都有值
     * status为200 返回True表示校验通过 返回False表示校验不通过，并且返回具体字段名称
     *
     * @param checkReq
     * @return
     */
    public CustomResult<CheckRequiredFieldRsp> checkRequiredField(CheckRequiredFieldReq checkReq) throws IllegalAccessException {
        CustomResult response = new CustomResult(true);
        CheckRequiredFieldRsp checkRequiredFieldRsp = new CheckRequiredFieldRsp();
        String object = checkReq.getObject();
        String productLineCode = checkReq.getProductLineCode();
        if (Func.isEmpty(object) || Func.isEmpty(productLineCode)) {
            response.setStatus(500);
            response.setMsg("object|productLineCode 参数缺少");
            response.fail();
            return response;
        }
        // 查询Object Setting中配置的必填字段
        BuObjectTemplateDTO buObjectTemplateDTO = new BuObjectTemplateDTO();
        buObjectTemplateDTO.setObject(object);
        buObjectTemplateDTO.setProductLineCode(productLineCode);
        buObjectTemplateDTO.setCaseType(checkReq.getCaseType());

        BuObjectTemplateAllDTO buObjects = checkReq.getBuObjects();
        if (Func.isEmpty(buObjects)) {
            if (Func.equals(OperationModeEnum.ChargeOnly.getOperationMode(), checkReq.getOperationMode())) {
                buObjectTemplateDTO.setObject("orderV2");
                buObjectTemplateDTO.setOrderModel("ChargeOrder");
                buObjects = buSettingService.getTemplateSettingListV2(buObjectTemplateDTO);
            } else {
                buObjects = buSettingService.getTemplateSettingList(buObjectTemplateDTO);
            }
        }
        if (Func.isEmpty(buObjects) || Func.isEmpty(buObjects.getObjectSettingList())) {
            response.setStatus(500);
            response.setMsg("查询Object Setting 配置失败");
            response.fail();
            return response;
        }
        // 存储所有需要校验的字段 attributeCode-attributeName
        Map<String, String> requiredFields = Maps.newHashMap();
        List<BuObjectSettingDTO> objectSettingList = buObjects.getObjectSettingList();
        if (Func.isEmpty(objectSettingList)) {
            return response;
        }
        String subObject = checkReq.getSubObject();
        if (Func.isNotEmpty(subObject)) {
            objectSettingList = objectSettingList.stream().filter(obj -> Func.equals(obj.getAttributeCode(), subObject)).collect(Collectors.toList());
        }
        if (Func.isEmpty(objectSettingList)) {
            return response;
        }
        // 递归遍历数据
        convertToMap(objectSettingList, requiredFields);
        if (Func.isEmpty(requiredFields)) {
            return response;
        }
        List<String> errorMessage = Lists.newArrayList();
        // 查询当前对象的信息,根据object查询对象信息
        if (Func.equals(Constants.BU_OBJECT.ORDER.CODE, object)) {
            errorMessage = checkOrderRequiredField(checkReq, requiredFields);
            checkRequiredFieldRsp.setErrorMessage(errorMessage);
        }
        if (Func.equals(Constants.BU_OBJECT.ENQUIRY.CODE, object)) {
            checkRequiredFieldRsp = checkEnquiryRequiredField(checkReq, requiredFields);
            errorMessage = checkRequiredFieldRsp.getErrorMessage();
        }
        response.setData(checkRequiredFieldRsp);
        String resultMessage = Func.isNotEmpty(errorMessage) ? "Operation failed, mandatory item(s) are not complete:  ".concat(errorMessage.stream().distinct().collect(Collectors.joining(";"))) : "";
        response.setMsg(resultMessage);
        response.setSuccess(true);
        return response;
    }


    public List<String> checkOrderRequiredField(CheckRequiredFieldReq checkReq, Map<String, String> requiredFields) throws IllegalAccessException {
        List<String> errorMessage = Lists.newArrayList();
        OrderDetailDto orderInfo = checkReq.getOrderInfo();
        if (Func.isEmpty(orderInfo)) {
            CustomResult<OrderDetailDto> orderResult = this.getOrderDetailInfo(checkReq.getObjectId(), checkReq.getToken(), false);
            if (Func.isEmpty(orderResult)) {
                return errorMessage;
            }
            orderInfo = orderResult.getData();
        }
        // 校验TestRequestContacts信息
        checkTestContactsRequired(orderInfo, requiredFields, errorMessage);
        // 校验ObjectSetting 中配置的字段
        final Field[] orderFields = orderInfo.getClass().getDeclaredFields();
        if (orderFields.length > 0) {
            for (Field orderFiled : orderFields) {
                ObjectSetting orderObjectSetting = orderFiled.getAnnotation(ObjectSetting.class);
                if (Func.isEmpty(orderObjectSetting)) {
                    continue;
                }
                final Field[] fields = orderFiled.getType().getDeclaredFields();
                if (fields.length > 0) {
                    // Order Person校验处理
                    if (Func.isNotEmpty(orderObjectSetting.code())) {
                        for (Field field : fields) {
                            ObjectSetting objectSetting = field.getAnnotation(ObjectSetting.class);
                            if (Func.isEmpty(objectSetting)) {
                                continue;
                            }
                            if (requiredFields.containsKey(orderObjectSetting.code())) {
                                orderFiled.setAccessible(true);
                                field.setAccessible(true);
                                if (Func.isEmpty(field.get(orderFiled.get(orderInfo)))) {
                                    errorMessage.add(requiredFields.get(orderObjectSetting.code()));
                                }
                            }
                        }
                    } else {
                        for (Field field : fields) {
                            ObjectSetting objectSetting = field.getAnnotation(ObjectSetting.class);
                            if (Func.isEmpty(objectSetting)) {
                                continue;
                            }
                            String objectSettingCode = objectSetting.code();
                            if (requiredFields.containsKey(objectSettingCode)) {
                                orderFiled.setAccessible(true);
                                field.setAccessible(true);
                                if (Func.isEmpty(field.get(orderFiled.get(orderInfo)))) {
                                    errorMessage.add(requiredFields.get(objectSettingCode));
                                }
                            }
                        }
                    }
                }
            }
        }
        // 校验DFF信息
        if (!Func.equals(OperationModeEnum.ChargeOnly.getOperationMode(), checkReq.getOperationMode())) {
            checkDffRequiredField(checkReq.getObjectId(), errorMessage, checkReq.getProductLineCode());
        }
        return errorMessage;
    }

    private void checkDffRequiredField(String objectId, List<String> errorMessage, String productLineCode) {
        // 查询当前订单+DFF form 对应的必填配置
        CustomResult<OrderProductRsp> productRspCustomResult = this.getProductSampleList(objectId);
        if (Func.isEmpty(productRspCustomResult) || Func.isEmpty(productRspCustomResult.getData())) {
            return;
        }
        List<ProductSampleRsp> productSampleRspList = productRspCustomResult.getData().getProductSampleRspList();
        if (Func.isEmpty(productSampleRspList)) {
            return;
        }
        productSampleRspList.stream().forEach(productSampleRsp -> {
            // 语言
            String languageCode = productSampleRsp.getLanguageCode();
            // dff form校验
            ProductInfo productInfo = productSampleRsp.getProduct();
            if (Func.isNotEmpty(productInfo)) {
                String dFFFormID = productInfo.getdFFFormID();
                Map<String, String> dffMandatoryFieldMap = this.getDffMandatoryField(languageCode, dFFFormID);
                if (Func.isNotEmpty(dffMandatoryFieldMap)) {
                    reflectObject(productInfo, errorMessage, dffMandatoryFieldMap);
                }
            }
            // dff grid 校验
            List<ProductSampleInfo> productSampleList = productSampleRsp.getProductSamples();
            if (Func.isNotEmpty(productSampleList)) {
                List<ProductInfo> newProductSampleList = Lists.newArrayList();
                productSampleList.stream().forEach(productSampleInfo -> {
                    ProductInfo newProductInfo = new ProductInfo();
                    Func.copy(productSampleInfo, newProductInfo);
                    newProductSampleList.add(newProductInfo);
                });

                String dFFFormID = productSampleList.get(0).getdFFFormID();
                Map<String, String> dffGridMandatoryFieldMap = this.getDffMandatoryField(languageCode, dFFFormID);
                newProductSampleList.stream().forEach(sample -> {
                    if (Func.isNotEmpty(dffGridMandatoryFieldMap)) {
                        reflectObject(sample, errorMessage, dffGridMandatoryFieldMap);
                    }
                });
                //查询BU DFF自定义配置 读取DffCustomizeColumns
                List<DffCustomizeColumn> dffCustomizeColumnsList = this.getDffCustomizeColumns(productLineCode);
                Map<String, String> selfDFFMap = new HashMap<>();
                if(Func.isNotEmpty(dffCustomizeColumnsList)){
                    dffCustomizeColumnsList.stream().filter(item ->
                            Func.equalsSafe(item.getMandatoryFlag(), "true")
                    ).forEach(item -> {
                        selfDFFMap.put(item.getFieldCode(), item.getDisplayName());
                    });
                    newProductSampleList.stream().forEach(sample -> {
                        if (Func.isNotEmpty(selfDFFMap)) {
                            reflectObject(sample, errorMessage, selfDFFMap);
                        }
                    });
                }
            }
        });
    }

    private List<String> checkEnquiryDffRequiredField(String objectId) {
        List<String> errorMessage = Lists.newArrayList();
        // 查询当前DFF form 对应的必填配置
        BaseResponse<EnquiryProductRsp> productRspCustomResult = iEnquiryService.getProductSampleList(objectId);
        if (Func.isEmpty(productRspCustomResult) || Func.isEmpty(productRspCustomResult.getData())) {
            return errorMessage;
        }
        List<EnquiryProductSampleRsp> productSampleRspList = productRspCustomResult.getData().getProductSampleRspList();
        if (Func.isEmpty(productSampleRspList)) {
            return errorMessage;
        }
        productSampleRspList.stream().forEach(productSampleRsp -> {
            // 语言
            String languageCode = productSampleRsp.getLanguageCode();
            // dff form校验
            EnquiryProductInfo productInfo = productSampleRsp.getProduct();
            if (Func.isNotEmpty(productInfo)) {
                ProductInfo newProductInfo = new ProductInfo();
                Func.copy(productInfo, newProductInfo);
                String dFFFormID = productInfo.getdFFFormID();
                Map<String, String> dffMandatoryFieldMap = this.getDffMandatoryField(languageCode, dFFFormID);
                if (Func.isNotEmpty(dffMandatoryFieldMap)) {
                    reflectObject(newProductInfo, errorMessage, dffMandatoryFieldMap);
                }
            }
            // dff grid 校验
            List<EnquiryProductSampleInfo> productSampleList = productSampleRsp.getProductSamples();
            if (Func.isNotEmpty(productSampleList)) {
                List<ProductInfo> newProductSampleList = Lists.newArrayList();
                productSampleList.stream().forEach(productSampleInfo -> {
                    ProductInfo newProductInfo = new ProductInfo();
                    Func.copy(productSampleInfo, newProductInfo);
                    newProductSampleList.add(newProductInfo);
                });

                String dFFFormID = productSampleList.get(0).getdFFFormID();
                Map<String, String> dffGridMandatoryFieldMap = this.getDffMandatoryField(languageCode, dFFFormID);
                newProductSampleList.stream().forEach(sample -> {
                    if (Func.isNotEmpty(dffGridMandatoryFieldMap)) {
                        reflectObject(sample, errorMessage, dffGridMandatoryFieldMap);
                    }
                });
            }
        });
        return errorMessage;
    }

    private void reflectObject(Object object, List<String> errorMessage, Map<String, String> dffMandatoryFieldMap) {
        if (Func.isNotEmpty(dffMandatoryFieldMap)) {
            try {
                Field[] fields = object.getClass().getDeclaredFields();
                for (Field field : fields) {
                    field.setAccessible(true);
                    if (dffMandatoryFieldMap.containsKey(field.getName()) && Func.isEmpty(field.get(object))) {
                        errorMessage.add(dffMandatoryFieldMap.get(field.getName()));
                    }
                }
            } catch (IllegalAccessException e) {
                logger.error("dff 字段反射失败" + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    public Map<String, String> getDffMandatoryField(String languageCode, String dFFFormID) {
        Map<String, String> dffMandatoryFieldMap = Maps.newHashMap();
        if (Func.isNotEmpty(LanguageType.findCode(languageCode))) {
            List<DFFFormRspDTO> dffFormRspList = dffClient.queryDFF(dFFFormID, LanguageType.findCode(languageCode).getLanguageId());
            if (Func.isNotEmpty(dffFormRspList)) {
                dffFormRspList.stream().filter(item ->
                        Func.isNotEmpty(item.getDisplayInSystem())
                                && Arrays.stream(item.getDisplayInSystem().split(",")).collect(Collectors.toList()).contains("2")
                                &&Func.equalsSafe(item.getMandatoryFlag(), 1)
                ).forEach(item -> {
                    dffMandatoryFieldMap.put(item.getFieldCode(), item.getDispalyName());
                });
            }
        }
        return dffMandatoryFieldMap;
    }


    public CheckRequiredFieldRsp checkEnquiryRequiredField(CheckRequiredFieldReq checkReq, Map<String, String> requiredFields) throws IllegalAccessException {
        CheckRequiredFieldRsp checkRequiredFieldRsp = new CheckRequiredFieldRsp();
        List<String> errorMessage = Lists.newArrayList();
        EnquiryInfoDTO enquiryInfo = checkReq.getEnquiryInfo();
        if (Func.isEmpty(enquiryInfo)) {
            return checkRequiredFieldRsp;
        }
        checkEnquiryTestContactsRequired(enquiryInfo, requiredFields, errorMessage);
        final Field[] enquiryFields = enquiryInfo.getClass().getDeclaredFields();
        if (enquiryFields.length > 0) {
            for (Field enquiryFiled : enquiryFields) {
                ObjectSetting enquiryObjectSetting = enquiryFiled.getAnnotation(ObjectSetting.class);
                if (Func.isEmpty(enquiryObjectSetting)) {
                    continue;
                }
                final Field[] fields = enquiryFiled.getType().getDeclaredFields();
                if (fields.length > 0) {
                    for (Field field : fields) {
                        ObjectSetting objectSetting = field.getAnnotation(ObjectSetting.class);
                        if (Func.isEmpty(objectSetting)) {
                            continue;
                        }
                        String objectSettingCode = objectSetting.code();
                        if (requiredFields.containsKey(objectSettingCode)) {
                            enquiryFiled.setAccessible(true);
                            field.setAccessible(true);
                            if (Func.isEmpty(field.get(enquiryFiled.get(enquiryInfo)))) {
                                errorMessage.add(requiredFields.get(objectSettingCode));
                            }
                        }
                    }
                }
            }
        }
        // 校验DFF信息
        List<String> dffCheckMessageList = checkEnquiryDffRequiredField(checkReq.getObjectId());
        if (Func.isNotEmpty(dffCheckMessageList)) {
//            errorMessage.addAll(dffCheckMessageList);
            checkRequiredFieldRsp.setDffCheckMessageList(dffCheckMessageList);
        }
        checkRequiredFieldRsp.setErrorMessage(errorMessage);
        return checkRequiredFieldRsp;
    }


    private void checkTestContactsRequired(OrderDetailDto orderInfo, Map<String, String> requiredFields, List<String> errorMessage) {

        List<TestRequestContactsInfo> testRequestContacts = Lists.newArrayList();
        if (Func.isNotEmpty(orderInfo.getTestRequest())) {
            testRequestContacts = orderInfo.getTestRequest().getTestRequestContactsInfos();
        }
        if (requiredFields.containsKey("qualificationType")) {
            if (Func.isEmpty(orderInfo.getTestRequest()) || Func.isEmpty(orderInfo.getTestRequest().getQualificationType()) || Func.equals(orderInfo.getTestRequest().getQualificationType(), "0")) {
                errorMessage.add("Qualification Type");
            }
        }
        if (requiredFields.containsKey("softCopyDeliverTo")) {
            if (Func.isEmpty(testRequestContacts)) {
                errorMessage.add("SoftCopy Deliver To");
            } else {
                List<TestRequestContactsInfo> filteredContacts = testRequestContacts.stream().filter(t -> ContactsType.check(t.getContactsType(), ContactsType.SoftCopy))
                        .collect(Collectors.toList());
                if (Func.isEmpty(filteredContacts) ||
                        (Func.isEmpty(filteredContacts.get(0).getDeliverTo()) && Func.isEmpty(filteredContacts.get(0).getDeliverOthers()))) {
                    errorMessage.add("SoftCopy Deliver To");
                }
            }
        }
        if (requiredFields.containsKey("hardCopyDeliverTo")) {
            if (Func.isEmpty(testRequestContacts)) {
                errorMessage.add("HardCopy Deliver To");
            } else {
                List<TestRequestContactsInfo> filteredContacts = testRequestContacts.stream().filter(t -> ContactsType.check(t.getContactsType(), ContactsType.HardCopy))
                        .collect(Collectors.toList());
                if (Func.isEmpty(filteredContacts) ||
                        (Func.isEmpty(filteredContacts.get(0).getDeliverTo()) && Func.isEmpty(filteredContacts.get(0).getDeliverOthers()))) {
                    errorMessage.add("HardCopy Deliver To");
                }
            }
        }
        if (requiredFields.containsKey("returnSampleTo")) {
            if (Func.isEmpty(testRequestContacts)) {
                errorMessage.add("Return Sample To");
            } else {
                List<TestRequestContactsInfo> filteredContacts = testRequestContacts.stream().filter(t -> ContactsType.check(t.getContactsType(), ContactsType.ReturnSample))
                        .collect(Collectors.toList());
                if (Func.isEmpty(filteredContacts) ||
                        (Func.isEmpty(filteredContacts.get(0).getDeliverTo()) && Func.isEmpty(filteredContacts.get(0).getDeliverOthers()))) {
                    errorMessage.add("Return Sample To");
                }
            }
        }
        if (requiredFields.containsKey("invoiceDeliverTo")) {
            if (Func.isEmpty(testRequestContacts)) {
                errorMessage.add("Invoice Deliver To");
            } else {
                List<TestRequestContactsInfo> filteredContacts = testRequestContacts.stream().filter(t -> ContactsType.check(t.getContactsType(), ContactsType.Invoice))
                        .collect(Collectors.toList());
                if (Func.isEmpty(filteredContacts) ||
                        (Func.isEmpty(filteredContacts.get(0).getDeliverTo()) && Func.isEmpty(filteredContacts.get(0).getDeliverOthers()))) {
                    errorMessage.add("Invoice Deliver To");
                }
            }
        }
        if (requiredFields.containsKey("prelimReport")) {
            if (Func.isEmpty(testRequestContacts)) {
                errorMessage.add("Prelim Report");
            } else {
                List<TestRequestContactsInfo> filteredContacts = testRequestContacts.stream().filter(t -> ContactsType.check(t.getContactsType(), ContactsType.PrelimReport))
                        .collect(Collectors.toList());
                if (Func.isEmpty(filteredContacts) ||
                        (Func.isEmpty(filteredContacts.get(0).getDeliverTo()) && Func.isEmpty(filteredContacts.get(0).getDeliverOthers()))) {
                    errorMessage.add("Prelim Report");
                }
            }
        }
        if (requiredFields.containsKey("prelimReportCC")) {
            if (Func.isEmpty(testRequestContacts)) {
                errorMessage.add("Prelim Report CC");
            } else {
                List<TestRequestContactsInfo> filteredContacts = testRequestContacts.stream().filter(t -> ContactsType.check(t.getContactsType(), ContactsType.PrelimReportCC))
                        .collect(Collectors.toList());
                if (Func.isEmpty(filteredContacts) ||
                        (Func.isEmpty(filteredContacts.get(0).getDeliverTo()) && Func.isEmpty(filteredContacts.get(0).getDeliverOthers()))) {
                    errorMessage.add("Prelim Report CC");
                }
            }
        }
        if (requiredFields.containsKey("softCopyDeliverCC")) {
            if (Func.isEmpty(testRequestContacts)) {
                errorMessage.add("SoftCopy Deliver CC");
            } else {
                List<TestRequestContactsInfo> filteredContacts = testRequestContacts.stream().filter(t -> ContactsType.check(t.getContactsType(), ContactsType.SoftCopyDeliveryCc))
                        .collect(Collectors.toList());
                if (Func.isEmpty(filteredContacts) ||
                        (Func.isEmpty(filteredContacts.get(0).getDeliverTo()) && Func.isEmpty(filteredContacts.get(0).getDeliverOthers()))) {
                    errorMessage.add("SoftCopy Deliver CC");
                }
            }
        }
        if (requiredFields.containsKey("returnSampleWay")) {
            if (Func.isEmpty(orderInfo.getTestRequest())) {
                errorMessage.add("Return Sample Way");
            } else {
                Integer returnResidueSampleFlag = orderInfo.getTestRequest().getReturnResidueSampleFlag();
                Integer returnTestedSampleFlag = orderInfo.getTestRequest().getReturnTestedSampleFlag();
                String returnResidueSampleRemark = orderInfo.getTestRequest().getReturnResidueSampleRemark();
                String returnTestedSampleRemark = orderInfo.getTestRequest().getReturnTestedSampleRemark();
                if (!Func.equalsSafe(returnResidueSampleFlag, 1) && !Func.equalsSafe(returnTestedSampleFlag, 1)
                        && Func.isEmpty(returnResidueSampleRemark) && Func.isEmpty(returnTestedSampleRemark)) {
                    errorMessage.add("Return Sample Way");
                }
            }
        }
    }


    private void checkEnquiryTestContactsRequired(EnquiryInfoDTO enquiryInfo, Map<String, String> requiredFields, List<String> errorMessage) {

        List<EnquiryTestRequestContactsInfo> testRequestContacts = Lists.newArrayList();
        if (Func.isNotEmpty(enquiryInfo.getTestRequest())) {
            testRequestContacts = enquiryInfo.getTestRequest().getTestRequestContactsInfos();
        }
        if (requiredFields.containsKey("qualificationType")) {
            if (Func.isEmpty(enquiryInfo.getTestRequest()) || Func.isEmpty(enquiryInfo.getTestRequest().getQualificationType()) || Func.equals(enquiryInfo.getTestRequest().getQualificationType(), "0")) {
                errorMessage.add("Qualification Type");
            }
        }
        if (requiredFields.containsKey("softCopyDeliverTo")) {
            if (Func.isEmpty(testRequestContacts)) {
                errorMessage.add("SoftCopy Deliver To");
            } else {
                List<EnquiryTestRequestContactsInfo> filteredContacts = testRequestContacts.stream().filter(t -> ContactsType.check(t.getContactsType(), ContactsType.SoftCopy))
                        .collect(Collectors.toList());
                if (Func.isEmpty(filteredContacts) ||
                        (Func.isEmpty(filteredContacts.get(0).getDeliverTo()) && Func.isEmpty(filteredContacts.get(0).getDeliverOthers()))) {
                    errorMessage.add("SoftCopy Deliver To");
                }
            }
        }
        if (requiredFields.containsKey("hardCopyDeliverTo")) {
            if (Func.isEmpty(testRequestContacts)) {
                errorMessage.add("HardCopy Deliver To");
            } else {
                List<EnquiryTestRequestContactsInfo> filteredContacts = testRequestContacts.stream().filter(t -> ContactsType.check(t.getContactsType(), ContactsType.HardCopy))
                        .collect(Collectors.toList());
                if (Func.isEmpty(filteredContacts) ||
                        (Func.isEmpty(filteredContacts.get(0).getDeliverTo()) && Func.isEmpty(filteredContacts.get(0).getDeliverOthers()))) {
                    errorMessage.add("HardCopy Deliver To");
                }
            }
        }
        if (requiredFields.containsKey("returnSampleTo")) {
            if (Func.isEmpty(testRequestContacts)) {
                errorMessage.add("Return Sample To");
            } else {
                List<EnquiryTestRequestContactsInfo> filteredContacts = testRequestContacts.stream().filter(t -> ContactsType.check(t.getContactsType(), ContactsType.ReturnSample))
                        .collect(Collectors.toList());
                if (Func.isEmpty(filteredContacts) ||
                        (Func.isEmpty(filteredContacts.get(0).getDeliverTo()) && Func.isEmpty(filteredContacts.get(0).getDeliverOthers()))) {
                    errorMessage.add("Return Sample To");
                }
            }
        }
        if (requiredFields.containsKey("invoiceDeliverTo")) {
            if (Func.isEmpty(testRequestContacts)) {
                errorMessage.add("Invoice Deliver To");
            } else {
                List<EnquiryTestRequestContactsInfo> filteredContacts = testRequestContacts.stream().filter(t -> ContactsType.check(t.getContactsType(), ContactsType.Invoice))
                        .collect(Collectors.toList());
                if (Func.isEmpty(filteredContacts) ||
                        (Func.isEmpty(filteredContacts.get(0).getDeliverTo()) && Func.isEmpty(filteredContacts.get(0).getDeliverOthers()))) {
                    errorMessage.add("Invoice Deliver To");
                }
            }
        }
        if (requiredFields.containsKey("prelimReport")) {
            if (Func.isEmpty(testRequestContacts)) {
                errorMessage.add("Prelim Report");
            } else {
                List<EnquiryTestRequestContactsInfo> filteredContacts = testRequestContacts.stream().filter(t -> ContactsType.check(t.getContactsType(), ContactsType.PrelimReport))
                        .collect(Collectors.toList());
                if (Func.isEmpty(filteredContacts) ||
                        (Func.isEmpty(filteredContacts.get(0).getDeliverTo()) && Func.isEmpty(filteredContacts.get(0).getDeliverOthers()))) {
                    errorMessage.add("Prelim Report");
                }
            }
        }
        if (requiredFields.containsKey("prelimReportCC")) {
            if (Func.isEmpty(testRequestContacts)) {
                errorMessage.add("Prelim Report CC");
            } else {
                List<EnquiryTestRequestContactsInfo> filteredContacts = testRequestContacts.stream().filter(t -> ContactsType.check(t.getContactsType(), ContactsType.PrelimReportCC))
                        .collect(Collectors.toList());
                if (Func.isEmpty(filteredContacts) ||
                        (Func.isEmpty(filteredContacts.get(0).getDeliverTo()) && Func.isEmpty(filteredContacts.get(0).getDeliverOthers()))) {
                    errorMessage.add("Prelim Report CC");
                }
            }
        }
        if (requiredFields.containsKey("softCopyDeliverCC")) {
            if (Func.isEmpty(testRequestContacts)) {
                errorMessage.add("SoftCopy Deliver CC");
            } else {
                List<EnquiryTestRequestContactsInfo> filteredContacts = testRequestContacts.stream().filter(t -> ContactsType.check(t.getContactsType(), ContactsType.SoftCopyDeliveryCc))
                        .collect(Collectors.toList());
                if (Func.isEmpty(filteredContacts) ||
                        (Func.isEmpty(filteredContacts.get(0).getDeliverTo()) && Func.isEmpty(filteredContacts.get(0).getDeliverOthers()))) {
                    errorMessage.add("SoftCopy Deliver CC");
                }
            }
        }
        if (requiredFields.containsKey("returnSampleWay")) {
            if (Func.isEmpty(enquiryInfo.getTestRequest())) {
                errorMessage.add("Return Sample Way");
            } else {
                Integer returnResidueSampleFlag = enquiryInfo.getTestRequest().getReturnResidueSampleFlag();
                Integer returnTestedSampleFlag = enquiryInfo.getTestRequest().getReturnTestedSampleFlag();
                String returnResidueSampleRemark = enquiryInfo.getTestRequest().getReturnResidueSampleRemark();
                String returnTestedSampleRemark = enquiryInfo.getTestRequest().getReturnTestedSampleRemark();
                if (!Func.equalsSafe(returnResidueSampleFlag, 1) && !Func.equalsSafe(returnTestedSampleFlag, 1)
                        && Func.isEmpty(returnResidueSampleRemark) && Func.isEmpty(returnTestedSampleRemark)) {
                    errorMessage.add("Return Sample Way");
                }
            }
        }
        if (requiredFields.containsKey("reportHeader")) {
            if (Func.isEmpty(enquiryInfo.getTestRequest()) || Func.isEmpty(enquiryInfo.getTestRequest().getReportHeader())) {
                errorMessage.add("Report Header");
            } else if (Func.equals(ReportLanguage.EnglishAndChineseReport.getCode(), enquiryInfo.getTestRequest().getReportLanguage())) {
                String[] reportHeaders = enquiryInfo.getTestRequest().getReportHeader().split("\\|\\|\\|");
                if (Func.isEmpty(reportHeaders) || reportHeaders.length != 2 || Func.isEmpty(reportHeaders[0]) || Func.isEmpty(reportHeaders[1])) {
                    errorMessage.add("Report Header");
                }
            }
        }
        if (requiredFields.containsKey("reportAddress")) {
            if (Func.isEmpty(enquiryInfo.getTestRequest()) || Func.isEmpty(enquiryInfo.getTestRequest().getReportDeliveredTo())) {
                errorMessage.add("Report Address");
            } else if (Func.equals(ReportLanguage.EnglishAndChineseReport.getCode(), enquiryInfo.getTestRequest().getReportLanguage())) {
                String[] reportHeaders = enquiryInfo.getTestRequest().getReportDeliveredTo().split("\\|\\|\\|");
                if (Func.isEmpty(reportHeaders) || reportHeaders.length != 2 || Func.isEmpty(reportHeaders[0]) || Func.isEmpty(reportHeaders[1])) {
                    errorMessage.add("Report Address");
                }
            }
        }
    }


    /**
     * 递归处理setting集合转换为Map
     *
     * @param objectSettingList
     * @param resultMap
     */
    public void convertToMap(List<BuObjectSettingDTO> objectSettingList, Map<String, String> resultMap) {
        if (Func.isNotEmpty(objectSettingList)) {
            objectSettingList = objectSettingList.stream().filter(item -> Func.isNotEmpty(item.getIsDisplay()) && item.getIsDisplay() == 1).collect(Collectors.toList());
            objectSettingList.stream().forEach(item -> {
                if (Func.isEmpty(item.getSubSettingList())) {
                    if (Func.isNotEmpty(item.getIsDisplay()) && item.getIsDisplay() == 1
                            && Func.isNotEmpty(item.getIsRequired()) && item.getIsRequired() == 1) {
                        resultMap.put(item.getAttributeCode(), item.getAttributeName());
                    }
                } else {
                    convertToMap(item.getSubSettingList(), resultMap);
                }
            });
        }
    }

    public CustomResult afterSendEmail(String orderNo) {
        OrderInfoPO orderInfoPO = orderMapper.getInfoByOrderNo(orderNo);
        BizLogInfo bizLog = new BizLogInfo();
        UserInfo user = tokenClient.getUser();
        bizLog.setBu((Func.isNotEmpty(orderInfoPO) && Func.isNotEmpty(orderInfoPO.getBUCode())) ? orderInfoPO.getBUCode() : user.getCurrentLabCode().split(" ")[1]);
        bizLog.setLab((Func.isNotEmpty(orderInfoPO) && Func.isNotEmpty(orderInfoPO.getLocationCode())) ? orderInfoPO.getLocationCode() :user.getCurrentLabCode().split(" ")[0]);
        bizLog.setOpUser(user.getRegionAccount());
        bizLog.setBizId(orderNo);
        bizLog.setOpType("Send Email");
        bizLog.setBizOpType(BizLogConstant.ORDER_OPERATION_HISTORY);
        bizLog.setNewVal("Send Voucher");
        bizLogClient.doSend(bizLog);
        CustomResult rspResult = new CustomResult();
        rspResult.setSuccess(true);
        return rspResult;
    }


    /**
     * renew时查询sample信息
     *
     * @param orderNo
     * @return
     */
    public BaseResponse getSampleInfoForRenew(String orderNo, String reportId) {
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setStatus(ResponseCode.SUCCESS.getCode());
        //参数校验
        if (Func.isEmpty(orderNo)) {
            baseResponse.setStatus(ResponseCode.FAIL.getCode());
            baseResponse.setMessage("orderId为空");
            return baseResponse;
        }
        //查询订单信息
        OrderInfoPO orderInfoPO = orderMapper.getOrderBaseInfo(orderNo);
        if (Func.isEmpty(orderInfoPO)) {
            baseResponse.setStatus(ResponseCode.FAIL.getCode());
            baseResponse.setMessage("订单信息查询失败");
            return baseResponse;
        }
        //查询report下sample
        ReportSampleReq reportSampleReq = new ReportSampleReq();
        reportSampleReq.setReportId(reportId);
        reportSampleReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<List<TestSampleRsp>> reportSampleResult = reportFacade.getSampleByReportId(reportSampleReq);
        if (Func.isEmpty(reportSampleResult) || !Func.equals(ResponseCode.SUCCESS.getCode(), reportSampleResult.getStatus())) {
            baseResponse.setStatus(ResponseCode.FAIL.getCode());
            baseResponse.setMessage("查询report下Sample失败");
            return baseResponse;
        }
        List<TestSampleRsp> testSampleRsps = reportSampleResult.getData();
        if (Func.isEmpty(testSampleRsps)) {
            baseResponse.setStatus(ResponseCode.FAIL.getCode());
            baseResponse.setMessage("report下sample为空");
            return baseResponse;
        }

        List<String> sampleNos = testSampleRsps.stream().map(testSampleRsp -> testSampleRsp.getSampleNo()).collect(Collectors.toList());
        String orderId = orderInfoPO.getID();
        //查询sample信息
        CustomResult<OrderProductRsp> productRspCustomResult = this.getProductSampleList(orderId);
        if (Func.isEmpty(productRspCustomResult) || !productRspCustomResult.isSuccess()) {
            baseResponse.setStatus(ResponseCode.FAIL.getCode());
            baseResponse.setMessage("查询sample信息失败");
            return baseResponse;
        }
        List<ProductSampleRsp> productSampleRspList = productRspCustomResult.getData().getProductSampleRspList();
        if (Func.isEmpty(productSampleRspList)) {
            baseResponse.setStatus(ResponseCode.FAIL.getCode());
            baseResponse.setMessage("查询sample信息失败");
            return baseResponse;
        }
        for (ProductSampleRsp productSampleRsp : productSampleRspList) {
            List<ProductSampleInfo> productSampleInfos = productSampleRsp.getProductSamples();
            //productSampleInfos = productSampleInfos.stream().filter(productSampleInfo -> sampleNos.contains(productSampleInfo.getSampleID())).collect(Collectors.toList());
            for (ProductSampleInfo objProductSampleInfo : productSampleInfos) {
                if (sampleNos.contains(objProductSampleInfo.getSampleID())) {
                    objProductSampleInfo.setDisabled(false);
                } else {
                    objProductSampleInfo.setDisabled(true);
                }
            }
            productSampleRsp.setProductSamples(productSampleInfos);
        }
        OrderProductForRenewRsp orderProductForRenewRsp = new OrderProductForRenewRsp();
        orderProductForRenewRsp.setProductSampleRspList(productSampleRspList);
        //查询客户信息，有oem取oem，没有取buyer/agent PK
        CustomerInfoExample customerInfoExample = new CustomerInfoExample();
        customerInfoExample.createCriteria().andGeneralOrderIDEqualTo(orderId);
        List<CustomerInfoPO> customerInfoPOS = customerInfoMapper.selectByExample(customerInfoExample);
        if (Func.isNotEmpty(customerInfoPOS)) {
            CustomerInfoPO oem = customerInfoPOS.stream().filter(customerInfoPO -> Func.equals(CustomerType.OEM.getCode(), customerInfoPO.getCustomerUsage())).findFirst().orElse(null);
            if (Func.isNotEmpty(oem)) {
                orderProductForRenewRsp.setCustomerId(oem.getCustomerID());
                orderProductForRenewRsp.setCustomerGroupId(oem.getCustomerGroupID());
            } else {
                CustomerInfoPO buyer = customerInfoPOS.stream().filter(customerInfoPO -> Func.equals(CustomerType.Buyer.getCode(), customerInfoPO.getCustomerUsage())).findFirst().orElse(null);
                CustomerInfoPO agent = customerInfoPOS.stream().filter(customerInfoPO -> Func.equals(CustomerType.Agent.getCode(), customerInfoPO.getCustomerUsage())).findFirst().orElse(null);
                if (Func.isEmpty(buyer) && Func.isNotEmpty(agent)) {
                    orderProductForRenewRsp.setCustomerId(agent.getCustomerID());
                    orderProductForRenewRsp.setCustomerGroupId(agent.getCustomerGroupID());
                } else if (Func.isNotEmpty(buyer) && Func.isEmpty(agent)) {
                    orderProductForRenewRsp.setCustomerId(buyer.getCustomerID());
                    orderProductForRenewRsp.setCustomerGroupId(buyer.getCustomerGroupID());
                } else if (Func.isNotEmpty(buyer) && Func.isNotEmpty(agent)) {
                    WeightByBuyerAndAgentReq weightByBuyerAndAgentReq = new WeightByBuyerAndAgentReq();
                    weightByBuyerAndAgentReq.setBuyerCode(buyer.getBuyerGroup());
                    weightByBuyerAndAgentReq.setAgentCode(agent.getBuyerGroup());
                    weightByBuyerAndAgentReq.setAction(CustomerWeightType.ServiceRequirement.getCode());
                    weightByBuyerAndAgentReq.setOrderId(orderId);
                    String customerGroupId = customerService.getWeightByBuyerAndAgent(weightByBuyerAndAgentReq);
                    orderProductForRenewRsp.setCustomerGroupId(customerGroupId);
                }
            }

        }
        baseResponse.setData(orderProductForRenewRsp);
        return baseResponse;

    }

    public BaseResponse editOrderDelayInfo(OperationRemarkDTO orderDelay) {
        BaseResponse response = new BaseResponse();
        String orderId = orderDelay.getId();
        if (Func.isEmpty(orderId)) {
            response.setStatus(500);
            response.setMessage("orderId 不能为空");
            return response;
        }
        SlOrderInfoPO slOrderInfo = new SlOrderInfoPO();
        slOrderInfo.setOrderId(orderId);
        slOrderInfo.setDelayReason(orderDelay.getRemark());
        slOrderInfo.setDelayType(orderDelay.getReasonType());
        int result = slOrderMapper.updateOrderDelayInfo(slOrderInfo);
        if (result > 0) {
            // 记录更新记录
            iOperationHistoryService.orderOperationHistorySave(orderDelay, OperationTypeEnums.DelayOrder.getStatus());
            return response;
        }
        response.setStatus(500);
        response.setMessage("更新失败");
        return response;
    }

    /**
     * 订单列表页编辑订单信息
     *
     * @param orderInfoEditDTO
     * @return
     */
    public BaseResponse editOrderInfo(OrderInfoEditDTO orderInfoEditDTO) {
        BaseResponse response = new BaseResponse();
        String orderId = orderInfoEditDTO.getId();
        if (Func.isEmpty(orderId)) {
            response.setStatus(500);
            response.setMessage("orderId 不能为空");
            return response;
        }
        String buCode = ProductLineContextHolder.getProductLineCode();
        if (Func.isEmpty(buCode)) {
            response.setStatus(500);
            response.setMessage("获取BU失败");
            return response;
        }
        //防止DB切换失败，先做一次查询
        slOrderMapper.getSlOrderByOrderId(orderId);
        transactionTemplate.execute(transaction -> {
            // 更新Order Delay 信息
            SlOrderInfoPO slOrderInfo = new SlOrderInfoPO();
            slOrderInfo.setOrderId(orderId);
            slOrderInfo.setDelayReason(orderInfoEditDTO.getRemark());
            slOrderInfo.setDelayType(orderInfoEditDTO.getReasonType());
            slOrderMapper.updateOrderDelayInfo(slOrderInfo);
            // 更新Remark信息
            orderMapper.updateOrderRemark(orderInfoEditDTO.getOrderRemark(), orderId);
            return true;
        });
        // 处理Tag信息
        saveOrderTag(orderId, orderInfoEditDTO.getTagValueJson(), buCode);

        return response;
    }

    public BaseResponse saveDeliverInfo(OrderDetailDto orderDetailDto) {
        UserInfo user = tokenClient.getUser();
        transactionTemplate.execute((trans) -> {
            TestRequestInfo testRequest = orderDetailDto.getTestRequest();
            List<TestRequestInfoContactsInfo> contacts = new ArrayList<>();
            List<TestRequestContactsInfo> testRequestContactsInfos = testRequest.getTestRequestContactsInfos();
            TestRequestContactsInfoExample objTestRequestContactsInfoExample = new TestRequestContactsInfoExample();
            objTestRequestContactsInfoExample.createCriteria().andOrderIdEqualTo(orderDetailDto.getHeaders().getOrderId());
            List<TestRequestContactsInfoPO> testRequestContactsInfoPODbS = testRequestContactsInfoMapper.selectByExample(objTestRequestContactsInfoExample);
            List<TestRequestContactsInfoPO> testRequestContactsInfoPOSaveList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(testRequestContactsInfos)) {
                testRequestContactsInfos.forEach(e -> {
                    TestRequestInfoContactsInfo testRequestInfoContactsInfo = new TestRequestInfoContactsInfo();
                    testRequestInfoContactsInfo.setId(e.getId());
                    testRequestInfoContactsInfo.setContactsType(e.getContactsType());
                    testRequestInfoContactsInfo.setDeliverTo(e.getDeliverTo());
                    testRequestInfoContactsInfo.setDeliverOthers(e.getDeliverOthers());
                    contacts.add(testRequestInfoContactsInfo);
                    TestRequestContactsInfoPO objUpdate = testRequestContactsInfoPODbS.stream().filter(e1 -> e1.getId().equals(e.getId())).findFirst().orElse(null);
                    if (objUpdate != null) {
                        objUpdate.setModifiedDate(new Date());
                        objUpdate.setModifiedBy(SecurityUtil.getUserAccount());
                        objUpdate.setDeliverTo(e.getDeliverTo());
                        objUpdate.setDeliverOthers(e.getDeliverOthers());
                        testRequestContactsInfoPOSaveList.add(objUpdate);
                    } else {
                        TestRequestContactsInfoPO objInsert = new TestRequestContactsInfoPO();
                        objInsert.setId(Func.randomUUID());
                        objInsert.setCreatedDate(new Date());
                        objInsert.setModifiedBy(SecurityUtil.getUserAccount());
                        objInsert.setContactsType(e.getContactsType());
                        objInsert.setDeliverOthers(e.getDeliverOthers());
                        objInsert.setOrderId(orderDetailDto.getHeaders().getOrderId());
                        objInsert.setDeliverTo(e.getDeliverTo());
                        testRequestContactsInfoPOSaveList.add(objInsert);
                    }
                });
                testRequestMapper.saveTestRequestContacts(testRequestContactsInfoPOSaveList);
            }
            this.setTestRequestContactsInfo(contacts, testRequest);
            testRequest.setModifiedBy(user.getRegionAccount());
            testRequest.setModifiedDate(new Date());
            testRequestMapper.updateDeliveInfo(testRequest);
            return 1;
        });
        return BaseResponse.newSuccessInstance(true);
    }

    /**
     * 查询订单下的配置的 SoftCopy Deliver To Cc
     *
     * @return
     */
    public BaseResponse<List<SoftDeliverEmailDTO>> queryOrderSoftDeliverEmail(OrderSoftDeliverEmailReq request) {
        BaseResponse<List<SoftDeliverEmailDTO>> response = new BaseResponse<>();
        List<String> orderNos = request.getOrderNos();
        if (Func.isEmpty(orderNos)) {
            response.setMessage("orderNos为空");
            response.setStatus(500);
            return response;
        }
        List<SoftDeliverEmailDTO> softDeliverEmailList = Lists.newArrayList();
        // 请求订单对应的TestRequest
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setBu(request.getProductLineCode());
        orderNos.stream().forEach(orderNo -> {
            Set<String> emailToSet = Sets.newHashSet();
            Set<String> emailCcSet = Sets.newHashSet();
            SoftDeliverEmailDTO softDeliverEmailDTO = new SoftDeliverEmailDTO();
            softDeliverEmailDTO.setOrderNo(orderNo);
            orderIdReq.setOrderNo(orderNo);
            BaseResponse<TestRequestInfo> testRequestResponse = orderService.queryRestRequestForPe(orderIdReq);
            // 如果没有配置ServiceRequirement,返回空列表
            if (Func.isEmpty(testRequestResponse) || Func.isEmpty(testRequestResponse.getData()) ||
                    Func.isEmpty(testRequestResponse.getData().getTestRequestContactsInfos())) {
                softDeliverEmailDTO.setEmailTo(emailToSet);
                softDeliverEmailDTO.setEmailCc(emailCcSet);
                softDeliverEmailList.add(softDeliverEmailDTO);
                return;
            }
            List<TestRequestContactsInfo> serviceRequirementContacts = testRequestResponse.getData().getTestRequestContactsInfos();
            //筛选softCopyTo
            TestRequestContactsInfo softCopyTo = serviceRequirementContacts.stream()
                    .filter(testRequestContactsInfo -> Func.equals(TestRequestContactsType.SOFTCOPYTO.getCode(), testRequestContactsInfo.getContactsType().byteValue()))
                    .findFirst().orElse(null);
            //筛选softCopyCc
            TestRequestContactsInfo softCopyCc = serviceRequirementContacts.stream()
                    .filter(testRequestContactsInfo -> Func.equals(TestRequestContactsType.SOFTCOPYCC.getCode(), testRequestContactsInfo.getContactsType().byteValue()))
                    .findFirst().orElse(null);
            if (Func.isNotEmpty(softCopyTo) || Func.isNotEmpty(softCopyCc)) {
                //查询订单信息
                BaseResponse<OrderAllDTO> orderInfoRes = orderService.getOrderForPe(orderIdReq);
                if (Func.isEmpty(orderInfoRes) || Func.isEmpty(orderInfoRes.getData())) {
                    return;
                }
                OrderAllDTO orderInfo = orderInfoRes.getData();
                //查询客户信息
                BaseResponse<List<CustomerInstanceDTO>> customerResponse = orderService.queryCustomerForPe(orderIdReq);
                if (Func.isNotEmpty(customerResponse) && Func.isNotEmpty(customerResponse.getData())) {
                    List<CustomerInstanceDTO> customerInstanceDTOS = customerResponse.getData();
                    CustomerInstanceDTO applicant = customerInstanceDTOS.stream()
                            .filter(e -> e.getCustomerUsage().equals(CustomerUsage.Applicant.getCode())).findFirst().orElse(null);
                    CustomerInstanceDTO payer = customerInstanceDTOS.stream()
                            .filter(e -> e.getCustomerUsage().equals(CustomerUsage.Payer.getCode())).findFirst().orElse(null);
                    CustomerInstanceDTO buyer = customerInstanceDTOS.stream()
                            .filter(e -> e.getCustomerUsage().equals(CustomerUsage.Buyer.getCode())).findFirst().orElse(null);
                    CustomerInstanceDTO subContract = customerInstanceDTOS.stream()
                            .filter(e -> e.getCustomerUsage().equals(CustomerUsage.SUBCONTRACTFROM.getCode())).findFirst().orElse(null);
                    // 设置EmailToSet
                    if (Func.isNotEmpty(softCopyTo) && Func.isNotEmpty(softCopyTo.getDeliverTo())) {
                        String[] softCopyToList = softCopyTo.getDeliverTo().split(",");
                        for (String deliver : softCopyToList) {
                            if (Func.equals(DeliverToType.APPLICANT.getCode(), deliver) && Func.isNotEmpty(applicant)) {
                                emailToSet.addAll(this.parseEmail(applicant.getContactPersonEmail()));
                            } else if (Func.equals(DeliverToType.PAYER.getCode(), deliver) && Func.isNotEmpty(payer)) {
                                emailToSet.addAll(this.parseEmail(payer.getContactPersonEmail()));
                            } else if (Func.equals(DeliverToType.BUYER.getCode(), deliver) && Func.isNotEmpty(buyer)) {
                                emailToSet.addAll(this.parseEmail(buyer.getContactPersonEmail()));
                            } else if (Func.equals(DeliverToType.SUBCONTRACT_FROM.getCode(), deliver) && Func.isNotEmpty(subContract)) {
                                emailToSet.addAll(this.parseEmail(subContract.getContactPersonEmail()));
                            } else if (Func.equals(DeliverToType.CS.getCode(), deliver)) {
                                emailToSet.addAll(this.parseEmail(orderInfo.getcSEmail()));
                            } else if (Func.equals(DeliverToType.SALES.getCode(), deliver)) {
                                //emailList.add(orderPO.getSalesEmail());//todo kevin
                            }
                        }
                    }
                    // 设置EmailCcSet
                    if (Func.isNotEmpty(softCopyCc) && Func.isNotEmpty(softCopyCc.getDeliverTo())) {
                        String[] softCopyCcList = softCopyCc.getDeliverTo().split(",");
                        for (String softCc : softCopyCcList) {
                            if (Func.equals(DeliverToType.APPLICANT.getCode(), softCc) && Func.isNotEmpty(applicant)) {
                                emailCcSet.addAll(this.parseEmail(applicant.getContactPersonEmail()));
                            } else if (Func.equals(DeliverToType.PAYER.getCode(), softCc) && Func.isNotEmpty(payer)) {
                                emailCcSet.addAll(this.parseEmail(payer.getContactPersonEmail()));
                            } else if (Func.equals(DeliverToType.BUYER.getCode(), softCc) && Func.isNotEmpty(buyer)) {
                                emailCcSet.addAll(this.parseEmail(buyer.getContactPersonEmail()));
                            } else if (Func.equals(DeliverToType.SUBCONTRACT_FROM.getCode(), softCc) && Func.isNotEmpty(subContract)) {
                                emailCcSet.addAll(this.parseEmail(subContract.getContactPersonEmail()));
                            } else if (Func.equals(DeliverToType.CS.getCode(), softCc)) {
                                emailCcSet.addAll(this.parseEmail(orderInfo.getcSEmail()));
                            } else if (Func.equals(DeliverToType.SALES.getCode(), softCc)) {
                                //emailList.add(orderPO.getSalesEmail());//todo kevin
                            }
                        }
                    }
                }
                // Others处理
                if (Func.isNotEmpty(softCopyTo) && Func.isNotEmpty(softCopyTo.getDeliverOthers())) {
                    String deliverOthers = softCopyTo.getDeliverOthers();
                    String[] deliverOtherList = deliverOthers.split(";");
                    for (String deliverOther : deliverOtherList) {
                        if (Func.isNotEmpty(deliverOther)) {
                            emailToSet.add(deliverOther.trim());
                        }
                    }
                }
                if (Func.isNotEmpty(softCopyCc) && Func.isNotEmpty(softCopyCc.getDeliverOthers())) {
                    String deliverOthers = softCopyCc.getDeliverOthers();
                    String[] deliverOtherList = deliverOthers.split(";");
                    // 如果EmailTo跟EmailCc中存在重复的地址那么去掉Cc中内容
                    for (String deliverOther : deliverOtherList) {
                        if (Func.isNotEmpty(deliverOther) && !emailToSet.contains(deliverOther.trim())) {
                            emailCcSet.add(deliverOther.trim());
                        }
                    }
                }
            }
            softDeliverEmailDTO.setEmailTo(emailToSet);
            softDeliverEmailDTO.setEmailCc(emailCcSet);
            softDeliverEmailList.add(softDeliverEmailDTO);
        });
        response.setData(softDeliverEmailList);
        return response;
    }

    /**
     * 解析邮箱
     *
     * @param email
     * @return
     */
    private List<String> parseEmail(String email) {
        List<String> emailList = Lists.newArrayList();
        if (Func.isEmpty(email)) {
            return emailList;
        }
        String[] emails = email.split(";");
        for (String emailText : emails) {
            if (Func.isNotEmpty(emailText)) {
                emailList.add(emailText.trim());
            }
        }
        return emailList;
    }

    /**
     * 订单取消前的校验
     *
     * @param orderId
     * @return
     */
    public BaseResponse orderCancelCheck(String orderId) {
        //参数校验
        if (Func.isEmpty(orderId)) {
            return BaseResponse.newFailInstance("参数为空");
        }
        OrderInfoPO orderInfoPO = orderInfoMapper.selectByPrimaryKey(orderId);
        if (Func.isEmpty(orderInfoPO)) {
            return BaseResponse.newFailInstance("订单信息查询失败");
        }
        Integer orderStatus = orderInfoPO.getOrderStatus();
        //查询BU配置
        CancelOrderConfig cancelOrderConfig = buSettingService.getCancelOrderConfig(ProductLineContextHolder.getProductLineCode());
        //校验订单状态
        if (!cancelOrderConfig.getCondition().contains(orderStatus)) {
            return BaseResponse.newFailInstance("订单状态不允许Cancel Order");
        }
        //判断 此订单是否为分包单
        GPOSubContractReq gpoSubContractReq = new GPOSubContractReq();
        gpoSubContractReq.setOrderNo(orderInfoPO.getOrderNo());
        BaseResponse<List<OrderSubContractRsp>> listBaseResponse = subContractFacade.queryAllSubContractByOrderNo(gpoSubContractReq);

        AtomicReference<Boolean> isGo = new AtomicReference<>(false);
        AtomicReference<Boolean> subcontractStatusFlag = new AtomicReference<>(false);
        listBaseResponse.getData().forEach(item -> {
            if (Func.isNotEmpty(item.getOrderStatus())) {
                Integer orderStatusByOrderNO = orderService.getOrderStatusByOrderNO(item.getOrderNo());
                if (!OrderStatus.checkStatus(orderStatusByOrderNO, OrderStatus.Cancelled)) {
                    isGo.set(true);
                }
            }
            if (Func.isEmpty(item.getStatus()) || (!Func.equals(SubcontractStatus.New.getCode(), item.getStatus()) && !Func.equals(SubcontractStatus.Cancelled.getCode(), item.getStatus()))) {
                subcontractStatusFlag.set(true);
            }
        });
        if (isGo.get()) {
            return BaseResponse.newFailInstance("请先Cancel对应的ReferenceNo!!");
        }
        if (subcontractStatusFlag.get()) {
            return BaseResponse.newFailInstance("请先Cancel对应的Subcontract!!");
        }
        return BaseResponse.newSuccessInstance(true);
    }


    /**
     * 订单cancel发送申请
     *
     * @param orderCancelReq
     * @return
     */
    public BaseResponse cancelApply(OrderCancelReq orderCancelReq) {
        //参数校验
        if (Func.isEmpty(orderCancelReq) || Func.isEmpty(orderCancelReq.getOrderId()) || Func.isEmpty(orderCancelReq.getApproverEmail())) {
            return BaseResponse.newFailInstance("参数异常");
        }
        UserInfo userInfo = SecurityUtil.getUser();
        //查询订单信息
        OrderInfoPO orderInfoPO = orderInfoMapper.selectByPrimaryKey(orderCancelReq.getOrderId());
        //外部号
        String externalOrderNo = this.getExternalOrderNo(orderInfoPO.getOrderNo());

        //GPO2-8949 针对已有的订单取消流程不可以重复申请

        if (Func.isNotEmpty(orderInfoPO)) {
            ProcessInstanceQueryReq processInstanceQueryReq = new ProcessInstanceQueryReq();
            processInstanceQueryReq.setProcessDefinitionKey(ProcessDefinitionKeyEnums.CANCEL_ORDER.getKey());
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("orderNo", orderInfoPO.getOrderNo());
            processInstanceQueryReq.setVariables(map);
            processInstanceQueryReq.setToken(SecurityUtil.getSgsToken());
            BaseResponse<List<ProcessInstanceInfo>> processInstanceInfos = workflowFacade.getProcessByKeyAndVariables(processInstanceQueryReq);
            if (processInstanceInfos.getData().size() > 0) {
                return BaseResponse.newFailInstance("订单流程正在审批中，无法操作Cancel");
            }

        }
        OrderCancelEmailParam orderCancelEmailParam = this.getOrderCancelEmailParam(externalOrderNo, orderCancelReq, userInfo, orderInfoPO);
        //workFlow开启
        BaseResponse<String> baseResponse = this.orderCancelWorkFlowStart(orderInfoPO, orderCancelReq, userInfo, orderCancelEmailParam);
        if (Func.isEmpty(baseResponse)) {
            return BaseResponse.newFailInstance("流程创建失败");
        } else if (!Func.equals(ResponseCode.SUCCESS.getCode(), baseResponse.getStatus())) {
            return BaseResponse.newFailInstance(baseResponse.getMessage());
        }
        String processInstanceId = baseResponse.getData();

        //发送邮件
        return this.orderCancelSendEmail(orderCancelEmailParam, externalOrderNo, orderCancelReq, userInfo, orderInfoPO, processInstanceId);

    }

    /**
     * 提前获取email参数，保存到workFlow
     *
     * @param externalOrderNo
     * @param orderCancelReq
     * @param userInfo
     * @param orderInfoPO
     * @return
     */
    private OrderCancelEmailParam getOrderCancelEmailParam(String externalOrderNo, OrderCancelReq orderCancelReq, UserInfo userInfo, OrderInfoPO orderInfoPO) {
        //查询是否有水单
        OrderPaidUpInfoExample orderPaidUpInfoExample = new OrderPaidUpInfoExample();
        orderPaidUpInfoExample.createCriteria().andOrderIdEqualTo(orderCancelReq.getOrderId()).
                andActiveIndicatorEqualTo(ActiveType.Enable.getStatus());
        List<OrderPaidUpInfoPO> orderPaidUpInfoPOS = orderPaidUpInfoMapper.selectByExample(orderPaidUpInfoExample);
        OrderCancelEmailParam orderCancelEmailParam = new OrderCancelEmailParam();
        orderCancelEmailParam.setApproverName(orderCancelReq.getApproverName());
        orderCancelEmailParam.setApplyName(userInfo.getRegionAccount());
        orderCancelEmailParam.setOrderNo(externalOrderNo);
        orderCancelEmailParam.setOrderStatus(OrderStatus.getOrderStatus(orderInfoPO.getOrderStatus()).getMessage());
        orderCancelEmailParam.setTotalAmount(this.getCurrencyByCode(orderInfoPO.getCurrencyID()) + " " + orderInfoPO.getTotalPrice());
        orderCancelEmailParam.setRemark(orderCancelReq.getRemark());
        orderCancelEmailParam.setPaidUp(Func.isEmpty(orderPaidUpInfoPOS) ? "NO" : "YES");
        List<EmailFile> emailFiles = new ArrayList<>();
        if (Func.isNotEmpty(orderCancelReq.getFileList())) {
            for (OrderCancelFileReq orderCancelFileReq : orderCancelReq.getFileList()) {
                EmailFile emailFile = new EmailFile();
                emailFile.setFileName(orderCancelFileReq.getFileName());
                emailFile.setFileKey(orderCancelFileReq.getCloudId());
                emailFiles.add(emailFile);
            }
            orderCancelEmailParam.setEmailFiles(emailFiles);
        }
        return orderCancelEmailParam;
    }

    /**
     * order cancel发送审批邮件
     *
     * @param externalOrderNo
     * @param orderCancelReq
     * @param userInfo
     * @param orderInfoPO
     * @param processInstanceId
     * @return
     */
    private BaseResponse orderCancelSendEmail(OrderCancelEmailParam orderCancelEmailParam, String externalOrderNo,
                                              OrderCancelReq orderCancelReq, UserInfo userInfo, OrderInfoPO orderInfoPO, String processInstanceId) {
        //发送审批邮件
//        String subject = "取消订单审批申请" + externalOrderNo;
        String subject = "订单取消申请：" + externalOrderNo;
        String templateCode = "GPO_OrderCancelApply";
        List<String> emailToList = new ArrayList<>();
        emailToList.add(orderCancelReq.getApproverEmail());
        //邮件参数
        Map<String, Object> params = new HashMap<>();
        params.put("approverName", orderCancelEmailParam.getApproverName());
        params.put("applyName", orderCancelEmailParam.getApplyName());
        params.put("orderNo", orderCancelEmailParam.getOrderNo());
        params.put("orderStatus", orderCancelEmailParam.getOrderStatus());
        params.put("totalAmount", orderCancelEmailParam.getTotalAmount());
        params.put("remark", orderCancelEmailParam.getRemark());
        params.put("paidUp", orderCancelEmailParam.getPaidUp());
        String baseId = Base64.getEncoder().encodeToString(processInstanceId.getBytes(StandardCharsets.UTF_8));
        params.put("approveUrl", interfaceConfig.getBaseUrl() + "/gpo-api/order/email/cancel/approve/" + baseId + "/" + orderInfoPO.getOrderNo() + "/" + ProductLineContextHolder.getProductLineCode() + "/" + orderCancelReq.getApproverEmail() + "/" + userInfo.getCurrentLabCode());
        params.put("extranetApproveUrl", interfaceConfig.getExtranetUrl() + "/gpo-api/order/email/cancel/approve/" + baseId + "/" + orderInfoPO.getOrderNo() + "/" + ProductLineContextHolder.getProductLineCode() + "/" + orderCancelReq.getApproverEmail() + "/" + userInfo.getCurrentLabCode());
//        params.put("approveUrl","http://localhost:8085/gpo-api/order/email/cancel/approve/" +baseId+ "/"+ orderInfoPO.getOrderNo() +"/" + ProductLineContextHolder.getProductLineCode()+ "/" + orderCancelReq.getApproverEmail() + "/" + userInfo.getCurrentLabCode()) ;
        params.put("rejectUrl", interfaceConfig.getBaseUrl() + "/gpo-api/order/email/cancel/reject/" + baseId + "/" + orderInfoPO.getOrderNo() + "/" + ProductLineContextHolder.getProductLineCode() + "/" + orderCancelReq.getApproverEmail() + "/" + userInfo.getCurrentLabCode());
        params.put("extranetRejectUrl", interfaceConfig.getExtranetUrl() + "/gpo-api/order/email/cancel/reject/" + baseId + "/" + orderInfoPO.getOrderNo() + "/" + ProductLineContextHolder.getProductLineCode() + "/" + orderCancelReq.getApproverEmail() + "/" + userInfo.getCurrentLabCode());
//        params.put("rejectUrl","http://localhost:8085/gpo-api/order/email/cancel/reject/" +baseId+ "/"+ orderInfoPO.getOrderNo() +"/" + ProductLineContextHolder.getProductLineCode()+ "/" + orderCancelReq.getApproverEmail()+ "/" + userInfo.getCurrentLabCode());

        EmailAutoSendDTO emailAutoSendDTO = new EmailAutoSendDTO();
        emailAutoSendDTO.setMailSubject(subject);
        emailAutoSendDTO.setTemplateCode(templateCode);
        emailAutoSendDTO.setMailTo(emailToList);
        emailAutoSendDTO.setSystemId(SgsSystem.GPO.getSgsSystemId() + "");
        emailAutoSendDTO.setTemplateVariables(params);
        emailAutoSendDTO.setBuCode(ProductLineContextHolder.getProductLineCode());
        LabInfoPO labInfoPO = this.getLabInfo(orderInfoPO.getID());
        if (Func.isNotEmpty(labInfoPO)) {
            emailAutoSendDTO.setLabCode(labInfoPO.getLabCode());
        }
        emailAutoSendDTO.setSendBy(userInfo.getRegionAccount());
        emailAutoSendDTO.setTemplateType(EmailTemplateTypeEnums.GENERAL.getCode());
        emailAutoSendDTO.setObjcetNo(externalOrderNo);
        if (Func.isNotEmpty(orderCancelEmailParam.getEmailFiles())) {
            List<EmailAttachmentDTO> ossFiles = new ArrayList<>();
            for (EmailFile emailFile : orderCancelEmailParam.getEmailFiles()) {
                EmailAttachmentDTO emailAttachmentDTO = new EmailAttachmentDTO();
                emailAttachmentDTO.setFileName(emailFile.getFileName());
                emailAttachmentDTO.setFileKey(emailFile.getFileKey());
                ossFiles.add(emailAttachmentDTO);
            }
            emailAutoSendDTO.setOssFiles(ossFiles);
        }

        SystemLog systemLog = new SystemLog();
        systemLog.setObjectType("sendOrderCancelApplyEmail");
        systemLog.setObjectNo(orderInfoPO.getOrderNo());
        systemLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        systemLog.setType(com.sgs.otsnotes.facade.model.enums.SystemLogType.API.getType());
        systemLog.setRemark("sendOrderCancelApplyEmail");
        systemLog.setCreateBy(userInfo.getRegionAccount());
        systemLog.setLocationCode(ProductLineContextHolder.getLocationCode());
        systemLog.setOperationType("Send Deliver Apply Email");
        String emailSendResult = notificationClient.sendMailForGeneral(emailAutoSendDTO);
        systemLog.setRequest(net.sf.json.JSONObject.fromObject(emailAutoSendDTO).toString());
        systemLog.setResponse(emailSendResult);
        systemLogHelper.save(systemLog);
        return BaseResponse.newSuccessInstance(true);
    }

    /**
     * 开启流程，返回ProcessInstanceInfoId
     *
     * @param orderInfoPO
     * @param orderCancelReq
     * @return
     */
    private BaseResponse<String> orderCancelWorkFlowStart(OrderInfoPO orderInfoPO, OrderCancelReq orderCancelReq, UserInfo userInfo, OrderCancelEmailParam orderCancelEmailParam) {
        StartProcessReq startProcessReq = new StartProcessReq();
        startProcessReq.setProcessDefinitionKey(ProcessDefinitionKeyEnums.CANCEL_ORDER.getKey());
        startProcessReq.setBusinessKey(orderInfoPO.getOrderNo());
        Map<String, Object> variables = new HashMap<>();
        variables.put("startUserAccount", userInfo.getRegionAccount());
        variables.put("startUserEmail", userInfo.getEmail());
        variables.put("orderCancelEmailParam", JSON.toJSONString(orderCancelEmailParam));
        variables.put("approvalResult", Constants.WORK_FLOW.APPROVAL_RESULT.PROCESSING);
        startProcessReq.setVariables(variables);
        Map<String, Object> startFormVariables = new HashMap<>();
        startFormVariables.put("cancelApprover", orderCancelReq.getApproverName());
        startFormVariables.put("orderNo", orderInfoPO.getOrderNo());
        startFormVariables.put("reason", orderCancelReq.getReasonTypeInfo());
        startFormVariables.put("remark", orderCancelReq.getRemark());
        startFormVariables.put("applicant", userInfo.getRegionAccount());
        startFormVariables.put("applicantEmail", userInfo.getEmail());
        startProcessReq.setStartFormVariables(startFormVariables);
        Map<String, Object> transientVariables = new HashMap<>();
        transientVariables.put("cancelApprover", orderCancelReq.getApproverName());
//        transientVariables.put("startUserAccount",userInfo.getRegionAccount());
//        transientVariables.put("startUserEmail",userInfo.getEmail());
        startProcessReq.setTransientVariables(transientVariables);
        startProcessReq.setToken(SecurityUtil.getSgsToken());
        BaseResponse<ProcessInstanceInfo> baseResponse = workflowFacade.startProcess(startProcessReq);
        if (Func.isEmpty(baseResponse)) {
            return BaseResponse.newFailInstance("流程启动失败");
        } else if (!Func.equals(ResponseCode.SUCCESS.getCode(), baseResponse.getStatus())) {
            return BaseResponse.newFailInstance(baseResponse.getMessage());
        }
        return BaseResponse.newSuccessInstance(baseResponse.getData().getId());
    }

    /**
     * 处理保存订单的Tag信息
     */
    private void saveOrderTag(String orderId, String tagValueJson, String buCode) {

        List<TagValueInfoPO> newTagValues = Lists.newArrayList();
        tagValueJson = this.saveTagValue(tagValueJson, buCode, TagObjectType.Order.getValue(), newTagValues);
        List<TagValueSelectDTO> tags = new ArrayList<>();
        if (Func.isNotEmpty(tagValueJson)) {
            tags = JSONObject.parseObject(tagValueJson, new TypeReference<List<TagValueSelectDTO>>() {
            });
        }
        // 处理订单Tag关系数据
        if (Func.isNotEmpty(orderId)) {
            ObjectTagInfoExample example = new ObjectTagInfoExample();
            ObjectTagInfoExample.Criteria criteria = example.createCriteria();
            criteria.andObjectIdEqualTo(orderId);
            objectTagInfoMapper.deleteByExample(example);
        }
        if (Func.isNotEmpty(tagValueJson)) {
            ObjectTagInfoPO orderTag = new ObjectTagInfoPO();
            orderTag.setId(Func.randomUUID());
            orderTag.setData(tagValueJson);
            orderTag.setObjectId(orderId);
            orderTag.setObject(TagObjectType.Order.getValue());
            orderTag.setActiveIndicator(new Byte("1"));
            orderTag.setCreatedDate(DateUtils.now());
            objectTagInfoMapper.insert(orderTag);
        }
        List<TagValueSelectDTO> finalTags = tags;
        taskExecutor.execute(() -> {
            //1、保存新Tag Value
            if (CollectionUtils.isNotEmpty(newTagValues)) {
                tagValueExtMapper.insertBatch(newTagValues);
            }
            //2、tag value 如果删除再开启的情况,启用原先删除的数据；
            List<String> tagValueIds = new ArrayList<>();
            finalTags.forEach(tag -> {
                if (Func.isNotEmpty(tag.getSelectedValue())) {
                    tagValueIds.addAll(tag.getSelectedValue());
                }
            });
            if (Func.isNotEmpty(tagValueIds)) {
                tagValueExtMapper.enableBatch(tagValueIds);
            }
        });
    }

    /**
     * 查询订单外部号
     *
     * @param orderNo
     * @return
     */
    private String getExternalOrderNo(String orderNo) {
        String externalOrderNo = orderNo;
        List<String> orderNos = new ArrayList<>();
        orderNos.add(orderNo);
        GpoExternalOrderNoReq externalOrderNoReq = new GpoExternalOrderNoReq();
        externalOrderNoReq.setOrderNoList(orderNos);
        List<GpoExternalOrderInfo> externalOrderInfoList = externalNoService.queryExternalOrder(externalOrderNoReq).getData();
        if (Func.isNotEmpty(externalOrderInfoList)) {
            externalOrderNo = externalOrderInfoList.get(0).getExternalOrderNo();
        }
        return externalOrderNo;
    }

    /**
     * 查询lab信息
     *
     * @param orderId
     * @return
     */
    private LabInfoPO getLabInfo(String orderId) {
        LabInfoExample labInfoExample = new LabInfoExample();
        labInfoExample.createCriteria().andGeneralOrderIDEqualTo(orderId);
        List<LabInfoPO> labInfoPOS = labInfoMapper.selectByExample(labInfoExample);
        if (Func.isNotEmpty(labInfoPOS)) {
            return labInfoPOS.get(0);
        } else {
            return null;
        }
    }

    /**
     * 获取币种
     *
     * @param currencyCode
     * @return
     */
    private String getCurrencyByCode(String currencyCode) {
        if (Func.isEmpty(currencyCode)) {
            return "";
        }
        String currency = "";
        switch (currencyCode) {
            case "CNY":
                currency = "￥";
                break;
            case "JPY":
                currency = "J￥";
                break;
            case "AUD":
                currency = "A$";
                break;
            case "EUR":
                currency = "€";
                break;
            case "HKD":
                currency = "HK$";
                break;
            case "USD":
                currency = "$";
                break;
        }
        return currency;
    }

    /**
     * 前端的数据请求和merge 放到后端来处理
     *
     * @param trfNo
     * @param source
     * @return
     */
    public List<SGSMartAttachmentDto> getTrfAttachmentListByTrfNo(String trfNo, Integer source) {
        return callTrfInterface.getTrfAttachmentList(trfNo);
    }


    public BaseResponse<OrderDetailConfigRsp> getOrderDetailConfig(OrderDetailConfigReq orderDetailConfigReq) {
        if (Func.isEmpty(orderDetailConfigReq)) {
            return BaseResponse.newFailInstance("请求参数不能为空");
        }
        OrderDetailConfigRsp orderDetailConfigRsp = new OrderDetailConfigRsp();
        List<DataDictionary> reportRequirementOptionList = new ArrayList<>();
        String orderReportRequirement = "";
        if (Func.isNotEmpty(orderDetailConfigReq.getOrderNo())) {
            TestRequestInfo testRequestByOrderNo = testRequestMapper.getTestRequestByOrderNo(orderDetailConfigReq.getOrderNo());
            if (Func.isNotEmpty(testRequestByOrderNo)) {
                orderReportRequirement = testRequestByOrderNo.getReportRequirement();
            }
        }

        List<ReportRequirementEnum> reportRequirementEnumList = ReportRequirementEnum.getListByOrderType(orderDetailConfigReq.getCaseType(), orderDetailConfigReq.getBusinessObject(), orderReportRequirement);
        if (Func.isNotEmpty(reportRequirementEnumList)) {
            for (ReportRequirementEnum reportRequirementEnum : reportRequirementEnumList) {
                DataDictionary dataDictionary = new DataDictionary();
                dataDictionary.setSysKey(reportRequirementEnum.getCode());
                dataDictionary.setSysValue(reportRequirementEnum.getMessage());
                if (reportRequirementEnumList.size() == 1) {
                    dataDictionary.setSysKeyDefaultFlag("1");
                }
                reportRequirementOptionList.add(dataDictionary);
            }
        }
        orderDetailConfigRsp.setReportRequirementOptionList(reportRequirementOptionList);
        return BaseResponse.newSuccessInstance(orderDetailConfigRsp);
    }

    public BaseResponse<List<AccreditationScopeDTO>> getAccreditationScope() {
        List<AccreditationScopeDTO> accreditationScopeDTOList = new ArrayList<>();
        // TODO-Trevor.Yuan 2022/12/20 待CS接口开发完成后替换成接口
        accreditationScopeDTOList.add(new AccreditationScopeDTO("1", "CNAS"));
        accreditationScopeDTOList.add(new AccreditationScopeDTO("3", "UKAS"));
        accreditationScopeDTOList.add(new AccreditationScopeDTO("5", "CMA"));
        return BaseResponse.newSuccessInstance(accreditationScopeDTOList);
    }


    public BaseResponse<Boolean> sendQuotationToCustomer(QuotationSendToCustomerReq sendReq) {
        logger.info("QuotationSendToCustomerReq {}", sendReq);
        BaseResponse<Boolean> baseResponse = new BaseResponse<>();
        String token = tokenClient.getToken();
        String productLineCode = ProductLineContextHolder.getProductLineCode();

        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setProductLineCode(productLineCode);
        buParamReq.setGroupCode(Constants.BU_PARAM.QUOTATION.GROUP);
        buParamReq.setParamCode(Constants.BU_PARAM.QUOTATION.PUSH_INVOICE.PUSH_RULE_CODE);
        List<BuParamValueRsp> pushInvoiceBuParams = frameWorkClient.getBuParams(buParamReq);

        if (Func.isEmpty(pushInvoiceBuParams) || Func.isEmpty(pushInvoiceBuParams.get(0).getParamValue())) {
            baseResponse = BaseResponse.newFailInstance("未找到Invoice推送规则");
            return baseResponse;
        }

        String paramValue = pushInvoiceBuParams.get(0).getParamValue();
        logger.info("batchSendQuotationToCustomer,config info={}", paramValue);
        List<QuotationSendBuParamsDto> sendParamList = JSON.parseObject(paramValue, new TypeReference<ArrayList<QuotationSendBuParamsDto>>() {
        });
        baseResponse = sendOrderQuotationToCustomerImpl(sendReq, sendParamList);

        return baseResponse;
    }


    public BaseResponse<Boolean> checkSendQuotationToCustomer(QuotationSendToCustomerReq sendReq) {
        logger.info("checkSendQuotationToCustomer {}", sendReq);
        BaseResponse<Boolean> baseResponse = new BaseResponse<>();
        String productLineCode = ProductLineContextHolder.getProductLineCode();

        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setProductLineCode(productLineCode);
        buParamReq.setGroupCode(Constants.BU_PARAM.QUOTATION.GROUP);
        buParamReq.setParamCode(Constants.BU_PARAM.QUOTATION.PUSH_INVOICE.PUSH_RULE_CODE);
        List<BuParamValueRsp> pushInvoiceBuParams = frameWorkClient.getBuParams(buParamReq);

        if (Func.isEmpty(pushInvoiceBuParams) || Func.isEmpty(pushInvoiceBuParams.get(0).getParamValue())) {
            baseResponse = BaseResponse.newFailInstance("未找到Invoice推送规则");
            return baseResponse;
        }

        OrderNoGetReq noGetReq = new OrderNoGetReq();
        noGetReq.setOrderNo(sendReq.getOrderNo());
        noGetReq.setIncludeExternalRef(true);
        noGetReq.setIncludeLab(false);
        SimpleOrderDto simpleOrder = standardOrderService.getOrderByNo(noGetReq);
        if (Func.isEmpty(simpleOrder)) {
            baseResponse = BaseResponse.newFailInstance("订单不存在");
            return baseResponse;
        }
        if (Func.isEmpty(simpleOrder.getReferenceList())) {
            baseResponse = BaseResponse.newFailInstance("未找到关联Ref No，无法推送");
            return baseResponse;
        }
        String paramValue = pushInvoiceBuParams.get(0).getParamValue();
        Integer orderStatus = simpleOrder.getOrderStatus();
        Set<Integer> refSystemIdList = ReferenceConvertor.toRefSystemIdList(simpleOrder.getReferenceList());
        logger.info("checkSendQuotationToCustomer,config info={},orderStatus={},refSystemIdList={}", paramValue, orderStatus, refSystemIdList);
        List<QuotationSendBuParamsDto> sendParamList = JSON.parseObject(paramValue, new TypeReference<ArrayList<QuotationSendBuParamsDto>>() {
        });
        boolean sendQuotationToCustomerFlag = false;
        for (QuotationSendBuParamsDto quotationSendBuParamsDto : sendParamList) {
            Integer refId = ObjectUtil.defaultIfNull(quotationSendBuParamsDto.getRefSystemId(), -1);
            List<Integer> statusList = ObjectUtil.defaultIfNull(quotationSendBuParamsDto.getStatusList(),new ArrayList<>());
            if (refSystemIdList.contains(refId) && statusList.contains(orderStatus)) {
                sendQuotationToCustomerFlag = true;
                break;
            }
        }
        if (!sendQuotationToCustomerFlag) {
            baseResponse = BaseResponse.newFailInstance("当前订单状态不满足推送报价单规则");
            return baseResponse;
        }
        return baseResponse;
    }


    public BaseResponse<Boolean> batchSendQuotationToCustomer(QuotationBatchSendToCustomerReq batchSendReq) {
        logger.info("QuotationBatchSendToCustomerReq {}", batchSendReq);
        BaseResponse<Boolean> baseResponse = new BaseResponse<>();
        if (Func.isEmpty(batchSendReq) || Func.isEmpty(batchSendReq.getOrderQuotationList())) {
            baseResponse = BaseResponse.newFailInstance("请求参数错误");
            return baseResponse;
        }


        String token = tokenClient.getToken();
        String productLineCode = ProductLineContextHolder.getProductLineCode();

        List<QuotationSendToCustomerReq> orderQuotationList = batchSendReq.getOrderQuotationList().parallelStream()
                .map(QuotationSendToCustomerReq::getOrderNo).distinct().map(orderNo -> {
                    QuotationSendToCustomerReq sendReq = new QuotationSendToCustomerReq();
                    sendReq.setProductLineCode(batchSendReq.getProductLineCode());
                    sendReq.setToken(batchSendReq.getToken());
                    sendReq.setOrderNo(orderNo);
                    return sendReq;
                }).collect(Collectors.toList());
        batchSendReq.setOrderQuotationList(orderQuotationList);

        int pushOrderLimit = getPushOrderLimit(productLineCode);
        if (orderQuotationList.size() > pushOrderLimit) {
            baseResponse = BaseResponse.newFailInstance(StrUtil.format("批量操作，暂只支持最多{}个Order，请重新勾选", pushOrderLimit));
            return baseResponse;
        }

        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setProductLineCode(productLineCode);
        buParamReq.setGroupCode(Constants.BU_PARAM.QUOTATION.GROUP);
        buParamReq.setParamCode(Constants.BU_PARAM.QUOTATION.PUSH_INVOICE.PUSH_RULE_CODE);
        List<BuParamValueRsp> pushInvoiceBuParams = frameWorkClient.getBuParams(buParamReq);
        if (Func.isEmpty(pushInvoiceBuParams) || Func.isEmpty(pushInvoiceBuParams.get(0).getParamValue())) {
            baseResponse = BaseResponse.newFailInstance("未找到Invoice推送规则");
            return baseResponse;
        }
        String paramValue = pushInvoiceBuParams.get(0).getParamValue();
        logger.info("batchSendQuotationToCustomer,config info={}", paramValue);
        List<QuotationSendBuParamsDto> sendParamList = JSON.parseObject(paramValue, new TypeReference<ArrayList<QuotationSendBuParamsDto>>() {
        });
        StringBuilder failMessage = new StringBuilder();
        int failCnt = 0;
        for (QuotationSendToCustomerReq quotationSendToCustomerReq : batchSendReq.getOrderQuotationList()) {
            quotationSendToCustomerReq.setToken(token);
            quotationSendToCustomerReq.setProductLineCode(productLineCode);
            BaseResponse<Boolean> itemResult = sendOrderQuotationToCustomerImpl(quotationSendToCustomerReq, sendParamList);
            logger.info("QuotationSendToCustomerReq ,orderNo={},itemResult={}", quotationSendToCustomerReq.getOrderNo(), itemResult.getMessage());
            if (!itemResult.isSuccess()) {
                failMessage.append(quotationSendToCustomerReq.getOrderNo()).append(StrPool.COLON).append(itemResult.getMessage()).append("<br />");
                failCnt++;
            }
        }
        if (failMessage.length() > 0) {
            baseResponse.setMessage("无法推送的订单如下：<br />" + failMessage);
        }
        if (failCnt == batchSendReq.getOrderQuotationList().size()) {
            baseResponse.setStatus(ResponseCode.FAIL.getCode());
        } else if (failCnt > 0 && failCnt < batchSendReq.getOrderQuotationList().size()) {
            baseResponse.setStatus(196);
        }
        return baseResponse;
    }

    private int getPushOrderLimit(String productLineCode) {
        int limit = 5;
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setProductLineCode(productLineCode);
        buParamReq.setGroupCode(Constants.BU_PARAM.QUOTATION.GROUP);
        buParamReq.setParamCode(Constants.BU_PARAM.QUOTATION.PUSH_INVOICE.PUSH_COUNT_LIMIT);
        List<BuParamValueRsp> pushInvoiceBuParams = frameWorkClient.getBuParams(buParamReq);
        if (Func.isEmpty(pushInvoiceBuParams) || Func.isEmpty(pushInvoiceBuParams.get(0).getParamValue())) {
            return limit;
        }
        String paramValue = pushInvoiceBuParams.get(0).getParamValue();
        paramValue = StrUtil.trimToEmpty(paramValue);
        limit = Func.toInt(paramValue, limit);
        return limit;
    }

    private BaseResponse<Boolean> sendOrderQuotationToCustomerImpl(QuotationSendToCustomerReq sendReq, List<QuotationSendBuParamsDto> sendParamList) {
        BaseResponse<Boolean> baseResponse = new BaseResponse<>();
        OrderNoGetReq noGetReq = new OrderNoGetReq();
        noGetReq.setOrderNo(sendReq.getOrderNo());
        noGetReq.setIncludeExternalRef(true);
        noGetReq.setIncludeLab(false);
        SimpleOrderDto simpleOrder = standardOrderService.getOrderByNo(noGetReq);
        if (Func.isEmpty(simpleOrder.getReferenceList())) {
            baseResponse = BaseResponse.newFailInstance("未找到关联Ref No");
            return baseResponse;
        }
        Integer orderStatus = simpleOrder.getOrderStatus();

        Set<Integer> refSystemIdList = ReferenceConvertor.toRefSystemIdList(simpleOrder.getReferenceList());
        boolean sendQuotationToCustomerFlag = false;
        for (QuotationSendBuParamsDto quotationSendBuParamsDto : sendParamList) {
            Integer refId = ObjectUtil.defaultIfNull(quotationSendBuParamsDto.getRefSystemId(), -1);
            List<Integer> statusList = ObjectUtil.defaultIfNull(quotationSendBuParamsDto.getStatusList(),new ArrayList<>());
            if (refSystemIdList.contains(refId) && statusList.contains(orderStatus)) {
                sendQuotationToCustomerFlag = true;
                break;
            }
        }
        if (!sendQuotationToCustomerFlag) {
            baseResponse = BaseResponse.newFailInstance("订单状态不满足报价单推送规则");
            return baseResponse;
        }
        OrderIdsRequest idsRequest = new OrderIdsRequest();
        idsRequest.setSystemId(SgsSystem.GPO.getSgsSystemId());
        idsRequest.setProductLineCode(sendReq.getProductLineCode());
        idsRequest.setOrderIds(Arrays.asList(simpleOrder.getId()));
        BaseResponse<List<QuotationHeadRsp>> activedQuotationResponse = quotationFacade.getActivedQuotationByOrderIds(idsRequest);
        if (Func.isNotEmpty(activedQuotationResponse.getData())) {
            boolean hasConfirmedQuotation = false;
            for (QuotationHeadRsp qh : activedQuotationResponse.getData()) {
                if (Func.equalsSafe(QuotationStatus.CONFIRM.getCode(), qh.getStatus())) {
                    hasConfirmedQuotation = true;
                    break;
                }
            }
            if (!hasConfirmedQuotation) {
                baseResponse = BaseResponse.newFailInstance("Quotation状态不满足推送报价单规则");
                return baseResponse;
            }
        }
        PreEvent preEvent = new PreEvent();
        preEvent.setEventSource(StandardObjectType.Quotation.getName());
        preEvent.setEventType(OrderStatus.getMessage(simpleOrder.getOrderStatus()));
        preEvent.setToken(sendReq.getToken());
        preEvent.setEventSourceStatus(simpleOrder.getOrderStatus());
        preEvent.setEventSourceNo(sendReq.getOrderNo());
        preEvent.setEventSourceId(simpleOrder.getId());
        preEvent.setOrderId(simpleOrder.getId());
        preEvent.setOrderNo(sendReq.getOrderNo());
        preEvent.setProductLineCode(sendReq.getProductLineCode());
//        eventCenterService.processEvent(preEvent);
        LabInfoPO labInfoPO = this.getLabInfo(simpleOrder.getId());
        scheduleTaskClient.sendEventMessage(preEvent, Func.isEmpty(labInfoPO) ? "" : labInfoPO.getLabCode());
        return baseResponse;
    }

    public CustomResult pushSkipOrder(ConfirmOrderReq request) {
        CustomResult customResult = CustomResult.newSuccessInstance();
        List<String> skipOrderList = orderDetailMapper.querySkipOrderList();
        List<String> errorMessage = Lists.newArrayList();
        if (Func.isNotEmpty(skipOrderList)) {
            ConfirmOrderReq confirmOrderReq = new ConfirmOrderReq();
            confirmOrderReq.setToken(request.getToken());
            confirmOrderReq.setProductLineCode(request.getProductLineCode());
            skipOrderList.stream().forEach(id -> {
                confirmOrderReq.setOrderId(id);
                BaseResponse createTrfRes = this.createTrfIfNecessary(confirmOrderReq);
                if (!createTrfRes.isSuccess()) {
                    errorMessage.add("[" + id + "]" + createTrfRes.getMessage());
                }
                logger.info("Skip createTrf response :{} ", createTrfRes);
            });
        }
        if (Func.isNotEmpty(errorMessage)) {
            customResult.setMsg(errorMessage.stream().collect(Collectors.joining("; ")));
        }
        return customResult;
    }

    public BaseResponse<Boolean> sendOrderDueDate(OrderIdReq orderIdReq, Date oldDueDate, String oldCsName, Boolean forceSend) {
        if (Func.isEmpty(orderIdReq) || Func.isEmpty(orderIdReq.getOrderId())) {
            return BaseResponse.newFailInstance("param.miss", null);
        }
        String orderId = orderIdReq.getOrderId();
        try {
            OrderDetailInfo orderDetailInfo = orderMapper.getOrderDetailInfo(orderId);
            if (Func.isEmpty(orderDetailInfo)) {
                logger.info("[{}]-sendOrderDueDate: NotFindOrder", orderId);
                return BaseResponse.newInstance(false);
            }

            Integer dueDateConfirmFlag = orderDetailInfo.getDueDateConfirmFlag();
            SlOrderInfoPO slOrderInfo = orderMapper.getSlOrderInfo(orderId);
            if (Func.isEmpty(slOrderInfo)) {
                logger.info("[{}]-sendOrderDueDate: NotFindOrder", orderId);
                return BaseResponse.newInstance(false);
            }
            if (!CaseType.check(slOrderInfo.getCaseType(), CaseType.IDB, CaseType.IDN)) {
                logger.info("[{}]-sendOrderDueDate: 非IDB/IDN单不支持SendDueDate", orderId);
                return BaseResponse.newFailInstance("非IDB/IDN单不支持SendDueDate");
            }
            //DueDate是否变化
            Date newOrderDueDate = orderDetailInfo.getExpectedOrderDueDate();
            if (Func.isEmpty(newOrderDueDate)) {
                return BaseResponse.newFailInstance("ExpectedOrderDueDate为空，无法操作SendDueDate");
            }
            //  是否ConfirmDueDate
            if (Func.isNotEmpty(dueDateConfirmFlag) && dueDateConfirmFlag != 1) {
                return BaseResponse.newFailInstance("未ConfirmOrderDueDate,无法SendDueDate");
            }
            if (!forceSend && DateUtils.compareDateTime(newOrderDueDate, oldDueDate) && (oldCsName == null || slOrderInfo.getcSName().equals(oldCsName))) {
                return BaseResponse.newFailInstance("ExpectedOrderDueDate或者CS没有变化，不需要推送");
            }
            List<OrderTrfRelInfo> trfList = orderTrfRelationshipMapper.getTrfRelationshipByOrderId(orderId);
            PreEvent preEvent = new PreEvent();
            preEvent.setEventSource(StandardObjectType.Order.getName());
            preEvent.setEventType(EventType.SendDueDate.getTypeName());
            preEvent.setToken(tokenClient.getToken());
            preEvent.setEventSourceNo(orderDetailInfo.getOrderNo());
            preEvent.setEventSourceId(orderDetailInfo.getOrderId());
            preEvent.setEventSourceStatus(orderDetailInfo.getOrderStatus());
            preEvent.setOrderId(orderDetailInfo.getOrderId());
            preEvent.setOrderNo(orderDetailInfo.getOrderNo());
            preEvent.setProductLineCode(orderDetailInfo.getBuCode());
            List<ExternalOrderDto> externalOrderList = ReferenceConvertor.toExternalOrderListByOrder(trfList);
            preEvent.setExternalOrderList(externalOrderList);
//            eventCenterService.processEvent(preEvent);
            LabInfoPO labInfoPO = this.getLabInfo(orderDetailInfo.getOrderId());
            scheduleTaskClient.sendEventMessage(preEvent, Func.isEmpty(labInfoPO) ? "" : labInfoPO.getLabCode());
        } catch (Exception e) {
            logger.error("[{}]-sendOrderDueDate error:{}", orderId, e);
            return BaseResponse.newSuccessInstance(false);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    public BaseResponse<Integer> orderDueDateConfirm(OrderDueDateConfirmReq orderDueDateConfirmReq) {
        if (Func.isEmpty(orderDueDateConfirmReq) || Func.isEmpty(orderDueDateConfirmReq.getOrderId())) {
            return BaseResponse.newFailInstance("param.miss", null);
        }
        if (Func.isNotEmpty(orderDueDateConfirmReq.getProductLineCode())) {
            ProductLineContextHolder.setProductLineCode(orderDueDateConfirmReq.getProductLineCode());
        }
        if (Func.isEmpty(orderDueDateConfirmReq.getTriggerAction())) {
            return BaseResponse.newFailInstance("未知的TriggerAction", null);
        }
        String triggerAction = orderDueDateConfirmReq.getTriggerAction();
        int result = 0;
        //根据BuParam配置判断是否更新DueDateConfirmFlag
        BuParamValueRsp buParamValue = frameWorkClient.getBuParamValue(ProductLineContextHolder.getProductLineCode(), "", Constants.BU_PARAM.DUE_DATE.GROUP, Constants.BU_PARAM.DUE_DATE.ORDER_DUE_DATE_CONFIRM_TRIGGER.CODE);
        if (Func.isNotEmpty(buParamValue) && StringUtils.equalsIgnoreCase(triggerAction, buParamValue.getParamValue())) {
            result = orderMapper.updateDueDateConfirmFlag(Lists.newArrayList(orderDueDateConfirmReq.getOrderId()));
        }
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderId(orderDueDateConfirmReq.getOrderId());

        BaseResponse<Boolean> sendOrderDueDateRsp = BaseResponse.newFailInstance(ResponseCode.UN_OPENED_ABILITY);
        if (StringUtils.equalsIgnoreCase(triggerAction, Constants.BU_PARAM.DUE_DATE.ORDER_DUE_DATE_CONFIRM_TRIGGER.VALUES.FIRST_PRINT_MASTER_LIST)) {
            if (result > 0) {
                sendOrderDueDateRsp = this.sendOrderDueDate(orderIdReq, null, null, true);
            } else {
                sendOrderDueDateRsp = BaseResponse.newFailInstance("不需要sendOrderDueDate");
            }
        } else if (StringUtils.equalsIgnoreCase(triggerAction, Constants.BU_PARAM.DUE_DATE.ORDER_DUE_DATE_CONFIRM_TRIGGER.VALUES.ORDER_CONFIRM)) {
            if (result > 0) {
                sendOrderDueDateRsp = this.sendOrderDueDate(orderIdReq, null, null, true);
            } else {
                sendOrderDueDateRsp = this.sendOrderDueDate(orderIdReq, orderDueDateConfirmReq.getOldOrderDueDate(), null, false);
            }
        } else {
            logger.info("暂不支持的OrderDueDateConfirmTrigger:{}", triggerAction);
        }
        logger.info("sendOrderDueDate res:{}", JSON.toJSONString(sendOrderDueDateRsp));
        return BaseResponse.newSuccessInstance(result);
    }

    public CustomResult specialToSGSMart(ConfirmOrderReq request) {
        CustomResult customResult = CustomResult.newSuccessInstance();
        String sgsToken = tokenClient.getToken();
        UserInfo userInfo = tokenClient.getUser(sgsToken);
        if (Func.isNotEmpty(request.getOrderId()) && Func.isNotEmpty(request.getOrderNo())) {
            OrderInfoPO orderInfo = orderInfoMapper.selectByPrimaryKey(request.getOrderId());
            String orderId = orderInfo.getID();
            CustomResult customResult1 = this.getOrderDetailInfo(orderId, tokenClient.getToken());
            OrderDetailDto orderDetailDto = (OrderDetailDto) customResult1.getData();
            OrderHeaderInfo headers = orderDetailDto.getHeaders();
            logger.info("createTrf request :orderNo={} ", headers.getOrderNo());
            if (Func.isEmpty(orderDetailDto.getProductSampleRspList())) {
                CustomResult<OrderProductRsp> productSampleList = this.getProductSampleList(orderId);
                List<ProductSampleRsp> productSampleRspList = productSampleList.getData().getProductSampleRspList();
                orderDetailDto.setProductSampleRspList(productSampleRspList);
            }
           /* BaseResponse<String> trfResponse = ilayerClient.createTrf(orderDetailDto);
            String trfNo = trfResponse.getData();*/
            //调用新工程创建TRF
            GpoSciOrderToTrfReq gpoSciOrderToTrfReq = new GpoSciOrderToTrfReq();
            gpoSciOrderToTrfReq.setRefSystemId(RefSystemIdEnum.SGSMart.getRefSystemId());
            gpoSciOrderToTrfReq.setOrderNo(headers.getOrderNo());
            gpoSciOrderToTrfReq.setToken(sgsToken);
            gpoSciOrderToTrfReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            BaseResponse<Order2TrfRsp> trfResponse = sciFacade.order2Trf(gpoSciOrderToTrfReq);
            String trfNo = "";
            if (trfResponse.isFail()) {
                customResult.setSuccess(false);
                customResult.setMsg(trfResponse.getMessage());
                return customResult;
            }
            trfNo = trfResponse.getData().getTrfNo();
            Integer refSystemId = trfResponse.getData().getRefSystemId();

//            BaseResponse<String> trfResponse = ilayerClient.createTrf(orderDetailDto);
//            String trfNo = trfResponse.getData();
            OrderTrfRelInfo trf = new OrderTrfRelInfo();
            trf.setOrderId(headers.getOrderId());
            trf.setRefNo(trfNo);
            trf.setRefSystemId(refSystemId);
            trf.setRefObjectType(RefObjectTypeEnum.Order.getType());
            trf.setRefObjectId(headers.getOrderId());
            trf.setCreatedBy(userInfo.getRegionAccount());
            trf.setCreatedDate(DateUtils.getNow());
            trf.setModifiedBy(userInfo.getRegionAccount());
            trf.setModifiedDate(DateUtils.getNow());
            trf.setTrfSourceType(TrfSourceType.Order2TRF.getSourceType());
            trf.setIntegrationChannel(RefIntegrationChannel.SCI.getCode());
            orderTrfRelationshipMapper.saveOrderTrfRelationshipBatch(Arrays.asList(trf));
            if (!orderInfo.getOrderStatus().equals(OrderStatusEnum.Confirmed.getStatus())) {
                PreEvent preEvent = new PreEvent();
                preEvent.setEventSource(StandardObjectType.Order.getName());
                //根据当前订单状态传值
                preEvent.setEventType(this.getEventTypeByOrderStatus(orderInfo.getOrderStatus()));
                preEvent.setEventSourceStatus(orderInfo.getOrderStatus());
                preEvent.setToken(sgsToken);
                preEvent.setEventSourceNo(orderInfo.getOrderNo());
                preEvent.setEventSourceId(orderInfo.getID());
                preEvent.setOrderId(orderInfo.getID());
                preEvent.setOrderNo(orderInfo.getOrderNo());
                preEvent.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                preEvent.setOperatorUsername("System");
//                eventCenterService.processEvent(preEvent);
                LabInfoPO labInfoPO = this.getLabInfo(orderInfo.getID());
                scheduleTaskClient.sendEventMessage(preEvent, Func.isEmpty(labInfoPO) ? "" : labInfoPO.getLabCode());

            }
        }
        return customResult;
    }

    private String getEventTypeByOrderStatus(Integer orderStatus) {
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.getOrderStatus(orderStatus);
        String eventType = null;
        switch (orderStatusEnum) {
            case Closed:
                eventType = EventType.Completed.getTypeName();
                break;
            case Testing:
                eventType = EventType.Testing.getTypeName();
                break;
            case Reporting:
                eventType = EventType.Reporting.getTypeName();
                break;
            case Completed:
                eventType = EventType.Completed.getTypeName();
                break;
            default:
                eventType = EventType.Confirmed.getTypeName();
                break;
        }
        return eventType;
    }

    public BaseResponse updateAndConfirmOrder(ConfirmAndUpdateOrderReq req) {
        if (Func.isEmpty(req.getOrderId())){
            return BaseResponse.newFailInstance("orderId is empty");
        }
        logger.info("By Clone updateAndConfirmOrder {}-{}-{}-{}",req.getOrderId(),req.getOrderNo(),req.getProductLineCode(),req.getToken());
        logger.info("By Clone updateAndConfirmOrder param {}",JSON.toJSONString(req));

        OrderDetailInfo orderInfo = orderDetailMapper.getOrderDetailInfo(req.getOrderId());
        if(Func.isEmpty(orderInfo)){
            logger.info("By Clone updateAndConfirmOrder orderDetail is null");
        }else{
            logger.info("By Clone updateAndConfirmOrder orderDetail {}",JSON.toJSONString(orderInfo));
        }

        //更新Sample Receive Date,Order Expect DueDate,TAT字段信息
        OrderInfoPO orderInfoPO = new OrderInfoPO();
        orderInfoPO.setID(req.getOrderId());
        orderInfoPO.setSampleReceiveDate(req.getSampleReceiveDate());
        orderInfoPO.setExpectedOrderDueDate(req.getExpectedOrderDueDate());
        orderInfoPO.setTAT(req.getTat());
        int updateOrderCount = orderDetailMapper.updateDateAndTatById(orderInfoPO);
        logger.info("updateAndConfirmOrder updateOrderCount:{}-{}",req.getOrderId(),updateOrderCount);

        //更新Auto Update Due Date、CS字段信息
        com.sgs.preorder.dbstorages.mybatis.model.SlOrderInfoPO slOrderInfoPO=new com.sgs.preorder.dbstorages.mybatis.model.SlOrderInfoPO();
        slOrderInfoPO.setGeneralOrderID(req.getOrderId());
        slOrderInfoPO.setDateEditFlag(req.getDateEditFlag());
        slOrderInfoPO.setCSName(req.getCSName());
        slOrderInfoPO.setResponsibleTeamCode(req.getResponsibleTeamCode());
        slOrderInfoPO.setCSContact(req.getCSContact());
        slOrderInfoPO.setCSEmail(req.getCSEmail());
        int updateSlOrderCount = orderDetailMapper.updateSlOrderByOrderId(slOrderInfoPO);
        logger.info("updateAndConfirmOrder updateSlOrderCount:{}-{}",req.getOrderId(),updateSlOrderCount);

        //ConfirmOrder
        ConfirmOrderReq confirmOrderReq = new ConfirmOrderReq();
        confirmOrderReq.setOldOrderId(req.getOldOrderId());
        confirmOrderReq.setOrderNo(req.getOrderNo());
        confirmOrderReq.setOrderId(req.getOrderId());
        confirmOrderReq.setProductLineCode(req.getProductLineCode());
        confirmOrderReq.setToken(req.getToken());
        confirmOrderReq.setUserInfo(req.getUserInfo());
        confirmOrderReq.setBizLogCloneFlag("By Clone " + req.getOldOrderNo());
        CustomResult objBaseResponse=this.confirmOrder(confirmOrderReq);
        return BaseResponse.newSuccessInstance(objBaseResponse);
    }
}
