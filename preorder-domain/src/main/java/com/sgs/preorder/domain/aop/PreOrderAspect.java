package com.sgs.preorder.domain.aop;

import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.core.annotation.AccessPolicyRule;
import com.sgs.preorder.core.common.HeaderHelper;
import com.sgs.preorder.core.common.UserHelper;
import com.sgs.preorder.core.constants.Constants;
import com.sgs.preorder.core.enums.PolicyType;
import com.sgs.preorder.core.util.AopUtils;
import com.sgs.preorder.core.util.ExceptionUtil;
import com.sgs.preorder.facade.model.info.DistLockInfo;
import com.sgs.preorder.integration.client.TokenClient;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.PriorityOrdered;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.UUID;

@Aspect
@Component
public class PreOrderAspect implements PriorityOrdered {
    private static final Logger logger = LoggerFactory.getLogger(PreOrderAspect.class);
    @Autowired
    private TokenClient tokenClient;
    @Autowired
    private RedissonClient redissonClient;

    /**
     *
     * @param reqObject
     */
    @Pointcut("(execution(* com.sgs.preorder.domain.service.*Service.*(*)) && args(reqObject)) || (execution(* com.sgs.preorder.domain.service.*.*Service.*(*)) && args(reqObject))")
    public void executeJoinPoint(BaseRequest reqObject) {

    }

    /**
     *
     * @param joinPoint
     * @param reqObject
     * @return
     */
    @Around("executeJoinPoint(reqObject)")
    public Object around(ProceedingJoinPoint joinPoint, BaseRequest reqObject){
        if (reqObject == null) {
            logger.error("{} Req: null", joinPoint.getSignature());
            return buildErrorResponse(ResponseCode.ILLEGAL_ARGUMENT, "request is null");
        }
        if (StringUtils.isBlank(reqObject.getRequestId())) {
            reqObject.setRequestId(UUID.randomUUID().toString());
        }
        // log日志配有"logPrefix"占位符
        MDC.put(Constants.LOG_PREFIX, getRequestFlag(reqObject));
        Object resp = null;
        RLock rLock = null;

        try {
            reqObject.validate();

            CustomResult rspResult = new CustomResult(false);
            Method targetMethod = AopUtils.getMethodFromTarget(joinPoint);
            if (targetMethod == null){
                rspResult.setMsg("请求Target Method 无效.");
                return rspResult;
            }
            String regionAccount = "";
            UserInfo user = tokenClient.getUser();
            if (user != null){
                regionAccount = user.getRegionAccount();
                UserHelper.setLocalUser(user);
            }
            CustomResult<DistLockInfo> lockResult = this.isDistLock(joinPoint.getTarget().getClass().getSimpleName(), targetMethod,joinPoint.getArgs());
            if (!lockResult.isSuccess()){
                rspResult.setMsg(lockResult.getMsg());
                return rspResult;
            }
            DistLockInfo distLock = lockResult.getData();
            if (Func.isNotEmpty(distLock) && distLock.isDistLock()){
                try {
                    logger.info("distLock:{}",distLock.getLockKey());
                    rLock = redissonClient.getLock(distLock.getLockKey());
                    if (!rLock.tryLock()){
                        rspResult.setMsg(String.format("请稍后当前用户(%s)在操作.", regionAccount));
                        return rspResult;
                    }
                }catch (Exception e){
                    e.printStackTrace();
                    logger.error(e.getMessage());
                }
            }
            resp = joinPoint.proceed(new Object[]{ reqObject });
        } catch (IllegalArgumentException ex) {
            resp = buildErrorResponse(ResponseCode.ILLEGAL_ARGUMENT, ex);
            logger.error("IllegalArgumentException req: {}，exception: {}", reqObject, ex);
        } catch (BizException ex) {
            // 前端可能将错误msg直接抛给用户
            resp = buildErrorResponse(ex.getErrorCode(), ex);
            logger.info("BizException req: {}, error:{}, exception: {}", reqObject.getRequestId(), ex.getMessage(), ex);
        } catch (Throwable ex) {
            // 前端可能将错误msg直接抛给用户
            resp = buildErrorResponse(ResponseCode.UNKNOWN, ex);
            logger.error("Throwable req. error:{}. req:{}, ex: {}", ex.getMessage(), reqObject, ex);
        } finally {
            if (rLock != null && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
            MDC.clear();
        }
        return resp;
    }

    /**
     *
     * @param className
     * @param targetMethod
     * @return
     */
    private CustomResult<DistLockInfo> isDistLock(String className, Method targetMethod,Object[] params){
        CustomResult<DistLockInfo> rspResult = new CustomResult(false);
        DistLockInfo distLock = new DistLockInfo();
        try {
            AccessPolicyRule accessPolicy = targetMethod.getAnnotation(AccessPolicyRule.class);
            if (accessPolicy == null){
                rspResult.setSuccess(true);
                return rspResult;
            }
            PolicyType policyType = accessPolicy.policyType();
            // 是否启用分步式锁
            boolean isDistLock = accessPolicy.isDistLock();
            if (!isDistLock){
                rspResult.setSuccess(true);
                return rspResult;
            }
            String bizId = HeaderHelper.getParameterValue(policyType.getCode());
            if(Func.isEmpty(bizId)){
                bizId = getValueCodeByObject(policyType.getCode(),params);
            }
            if (StringUtils.isBlank(bizId)){
                rspResult.setMsg(String.format("请求的当前%s不能为空.", policyType.getCode()));
                return rspResult;
            }
            distLock.setDistLock(isDistLock);
            distLock.setLockKey(policyType.getLockKey(className, targetMethod.getName(), bizId));
            rspResult.setSuccess(true);
        }catch (Exception ex){
            rspResult.setSuccess(false);
            rspResult.setMsg(String.format("%s.%s，Error: %s", className, targetMethod.getName(), ex.getMessage()));
            logger.error("PreOrderAspect.isDistLock({}.{})，Error:{}", className, targetMethod.getName(), ex);
        }
        rspResult.setData(distLock);
        return rspResult;
    }

    private String getValueCodeByObject(String valueType,Object[] params) {
        if(Func.isEmpty(params)){
            return "";
        }
        for (Object obj:params){
            if(obj == null){
                continue;
            }
            Class objClazz = (Class) obj.getClass();
            try {
                Field field = objClazz.getDeclaredField(valueType);
                field.setAccessible(true);
                Object value = field.get(obj);
                if(Func.isNotEmpty(value)){
                    return Func.toStr(value);
                }
            }catch (Exception e) {
                logger.error(e.getMessage(),e);
            }
        }
        return "";
    }


    /**
     *
     * @param errorCode
     * @param ex
     * @return
     */
    private CustomResult buildErrorResponse(ResponseCode errorCode, Throwable ex){
        CustomResult rspResult = new CustomResult();
        String localMsg = ex.getLocalizedMessage();
        if (StringUtils.isBlank(localMsg)){
            localMsg = ex.getMessage();
        }
        rspResult.setMsg(localMsg);
        rspResult.setStackTrace(ExceptionUtil.getStackTrace(ex));
        rspResult.setSuccess(errorCode.getCode() == 200);
        return rspResult;
    }

    /**
     *
     * @param errorCode
     * @param errorMsg
     * @return
     */
    private CustomResult buildErrorResponse(ResponseCode errorCode, String errorMsg){
        CustomResult rspResult = new CustomResult();
        rspResult.setMsg(errorMsg);
        rspResult.setSuccess(errorCode.getCode() == 200);
        return rspResult;
    }

    /**
     *
     * @param req
     * @return
     */
    private String getRequestFlag(BaseRequest req) {
        return req.getRequestId();
    }

    /**
     *
     * @param reqObject
     */
    @After("executeJoinPoint(reqObject)")
    public void after(BaseRequest reqObject) {
        UserHelper.clear();
    }

    @Override
    public int getOrder() {
        return Integer.MAX_VALUE;
    }
}
