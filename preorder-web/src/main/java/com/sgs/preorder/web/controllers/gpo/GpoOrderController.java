package com.sgs.preorder.web.controllers.gpo;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.redis.utils.RedisUtil;
import com.sgs.framework.security.annotation.AccessRule;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.grus.bizlog.common.BizLog;
import com.sgs.grus.bizlog.common.BizLogHelper;
import com.sgs.otsnotes.facade.model.dto.OrderExpectDueDateDTO;
import com.sgs.otsnotes.facade.model.rsp.TestLineLabSectionRsp;
import com.sgs.preorder.core.base.BaseController;
import com.sgs.preorder.core.config.OnlineConfig;
import com.sgs.preorder.core.constants.Constants;
import com.sgs.preorder.core.enums.BizLogConstant;
import com.sgs.preorder.dbstorages.mybatis.model.CrossLabRelPO;
import com.sgs.preorder.domain.dto.ValidateBindTrfStatusReq;
import com.sgs.preorder.domain.dto.buparams.OrderCreateMethodBuParamsDto;
import com.sgs.preorder.domain.service.*;
import com.sgs.preorder.domain.service.orderoperation.IOrderOperationService;
import com.sgs.preorder.domain.service.productlineservice.ProductLineServiceHolder;
import com.sgs.preorder.facade.model.dto.order.*;
import com.sgs.preorder.facade.model.enums.OrderStatus;
import com.sgs.preorder.facade.model.info.LabInfo;
import com.sgs.preorder.facade.model.info.SampleReceiveTimeInfo;
import com.sgs.preorder.facade.model.info.user.UserLabBuInfo;
import com.sgs.preorder.facade.model.req.*;
import com.sgs.preorder.facade.model.req.order.GenerateChargeOrderNoReq;
import com.sgs.preorder.facade.model.req.order.OrderCancelReq;
import com.sgs.preorder.facade.model.req.order.UpdatePaymentStatusReq;
import com.sgs.preorder.facade.model.req.sci.SciToOrderDetailReq;
import com.sgs.preorder.facade.model.req.trf.UpdateTrfStatusReq;
import com.sgs.preorder.facade.model.rsp.*;
import com.sgs.preorder.integration.client.FrameWorkClient;
import com.sgs.preorder.integration.client.UserClient;
import com.sgs.preorder.web.barcode.BarCodeTool;
import com.sgs.priceengine.facade.model.DTO.QuotationMatrixDTO;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@ApiSort(03)
@Api(value = "订单查询及操作相关API", tags = {Constants.SWAGGER.MODULAR.ORDER})
@RestController
@RequestMapping(value = "/order")
@AllArgsConstructor
@Slf4j
//@AccessRule
public class GpoOrderController extends BaseController {
	private OrderDetailService orderDetailService;
	private FrameWorkClient frameWorkClient;
	private ExpectDueDateService expectDueDateService;
	private OrderService orderService;
	private CrossLabService crossLabService;
	private StatusService statusService;
	private BarCodeTool barCodeTool;
	private UserClient userClient;

	private OnlineConfig onlineConfig;

	private StandardOrderService standardOrderService;
	private RedisUtil redisUtil;
	/**
	 *
	 * @param reportReceiverReq
	 * @return
	 */
	@PostMapping("/report-receiver")
	//@AccessRule(needProductLine = false,needToken = false)
	@ApiOperation(value = "【BU】【Token】查询Order Report Info信息", notes = "查询Order Report Info 信息")
	public BaseResponse<ReportReceiverRsp> getOrderDetailInfo(@RequestBody ReportReceiverReq reportReceiverReq){
		return orderService.getReportReceiverInfo(reportReceiverReq);
	}

	/**
	 *
	 * @param subcontractDetailReq
	 * @return
	 */
	@PostMapping("/subcontract-detail")
//	@AccessRule(needProductLine = false,needToken = false)
	@ApiOperation(value = "【BU】【Token】查询 Order Subcontract Info信息", notes = "查询 Order Subcontract Info 信息")
	public BaseResponse<SubcontractDetailRsp> getSubcontractDetailInfo(@RequestBody SubcontractDetailReq subcontractDetailReq){
		return orderService.getSubcontractDetail(subcontractDetailReq);
	}

	/**
	 *
	 * @param orderId
	 * @return
	 */
	@GetMapping(Constants.PATH_VARIABLES_BUCODE+"/detail")
	@ApiOperation(value = "【BU】【Token】查询Order Detail信息", notes = "查询Order Detail信息")
	public BaseResponse<OrderDetailDto> getOrderDetailInfo(@PathVariable(name = Constants.BUCODE) String productLineCode,@RequestParam(name = "orderId",required = false) String orderId){
		return BaseResponse.newSuccessInstance(orderDetailService.getOrderDetailInfo(orderId));
	}

	/**
	 *
	 * @param orderId
	 * @return
	 */
		@GetMapping("/productSample/"+Constants.PATH_VARIABLES_BUCODE+"/detail/{orderId}")
	@ApiOperation(value = "【BU】【Token】查询productSample信息", notes = "查询productSample信息")
	public BaseResponse<OrderProductRsp> getProductSampleList(@PathVariable(name = Constants.BUCODE) String productLineCode,@PathVariable(name = "orderId") String orderId){
		return BaseResponse.newSuccessInstance(orderDetailService.getProductSampleList(orderId));
	}

	/**
	 *
	 * @return
	 */
	@GetMapping(Constants.PATH_VARIABLES_BUCODE+"/tops")
	@ApiOperation(value = "【BU】【Token】查询Tops信息", notes = "查询Tops信息")
	public BaseResponse<TopRsp> getTops(@PathVariable(name = Constants.BUCODE) String productLineCode,@RequestParam(name="orderNo",required = false) String orderNo){
		UserInfo user = SecurityUtil.getUser();
		if(user==null){
			BaseResponse baseResponse = BaseResponse.newFailInstance(ResponseCode.ILLEGAL_ARGUMENT);
			baseResponse.setData("NoTops");
			return baseResponse;
		}
		String tops = "";
		Map<String, List<String>> dimensionMap = user.getDimensionMap();
		if(dimensionMap!=null){
			List<String> block_tops = dimensionMap.get("block_tops");
			if(CollectionUtils.isNotEmpty(block_tops)){
				tops=block_tops.stream().findFirst().orElse(null);
			}
		}
		UserLabBuInfo userLabBu = userClient.getUserLabBuInfo(SecurityUtil.getSgsToken());

		if(StringUtils.isNotEmpty(tops)){
			BaseResponse baseResponse = BaseResponse.newFailInstance(ResponseCode.ILLEGAL_ARGUMENT);
			baseResponse.setData("NoTops");
			return baseResponse;
		}

		TopRsp topDTO=new TopRsp();
		LabDTO objLabDTO=new LabDTO();
		objLabDTO.setBuCode(String.valueOf(userLabBu.getProductLineId()));
		List<LabInfo> labInfo = frameWorkClient.getLabInfo(objLabDTO);
		List<TopDetailRsp> topDetailDTOS = Lists.newArrayList();
		if(labInfo!=null){
			labInfo.stream().filter(lab -> StringUtils.isNotBlank(lab.getBossLocationCode()))
					.forEach(lab->{
						TopDetailRsp objTopDTO=new TopDetailRsp();
						objTopDTO.setText("TL"+lab.getBossLocationCode());
						objTopDTO.setValue(lab.getLaboratoryCode());
						topDetailDTOS.add(objTopDTO);
					});
			topDTO.setTopDetailDTOs(topDetailDTOS);
		}

		if(StringUtils.isNotEmpty(orderNo)){
			List<CrossLabRelPO> lab = crossLabService.getToLabByOrder(orderNo);
			if(CollectionUtils.isNotEmpty(lab)){
				String toLab = lab.get(0).getToLab();
				topDTO.setToLab(toLab);
			}
		}
		return BaseResponse.newSuccessInstance(topDTO);
	}


	//TATExpectDueDate计算规则
	@PostMapping(Constants.PATH_VARIABLES_BUCODE+ "/calculate")
	@ApiOperation(value = "【BU】【Token】 计算DueDate")
	public @ResponseBody BaseResponse<List<SampleReceiveTimeInfo>> calExpectDueDate(@PathVariable(name = Constants.BUCODE) String productLineCode,@RequestBody CalExpectDueDateReq calExpectDueDateReq){
		List<SampleReceiveTimeInfo> list = expectDueDateService.calExpectDueDate(calExpectDueDateReq);
		return BaseResponse.newSuccessInstance(list);
	}

	@PostMapping("/dueDate/calculate")
	@ApiOperation(value = "外部系统执行计算DueDate")
	@AccessRule(needToken = false,needProductLine = false)
	public @ResponseBody BaseResponse<List<SampleReceiveTimeInfo>> calExpectDueDateExt(@RequestBody CalExpectDueDateReq calExpectDueDateReq){
		List<SampleReceiveTimeInfo> list = expectDueDateService.calExpectDueDate(calExpectDueDateReq);
		return BaseResponse.newSuccessInstance(list);
	}

	@PostMapping(Constants.PATH_VARIABLES_BUCODE+"/validateBindTrfStatus/{orderId}")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "orderId", value = "orderId", paramType = "path",
					dataType = "string", required = true)
	})
	@ApiOperation("【BU】【Token】 Validate Bind Trf")
	public BaseResponse validateBindTrfStatus(@PathVariable(name="orderId") String orderId, @RequestBody ValidateBindTrfStatusReq validateBindTrfStatusReq){
		if(Func.isEmpty(validateBindTrfStatusReq)){
			validateBindTrfStatusReq = new ValidateBindTrfStatusReq();
			validateBindTrfStatusReq.setOrderId(orderId);
		}
		return  BaseResponse.newSuccessInstance(orderService.validateBindTrfStatus(validateBindTrfStatusReq));
	}


	/**
	 * 获取订单信息和customer 信息
	 * @param req
	 * @return
	 */
	@PostMapping(Constants.PATH_VARIABLES_BUCODE+"/getOrderForPe")
	@ApiOperation("【BU】【Token】获取订单信息和customer 信息")
	public BaseResponse<OrderAllDTO> getOrderForPe(@RequestBody OrderIdReq req){
		return orderService.getOrderForPe(req);
	}


	/**
	 * 取消转单
	 * @param cancelCrossOrderReq
	 * @return
	 */
	@BizLog(bizType = BizLogConstant.ORDER_OPERATION_HISTORY, operType = "Cancel Tops")
	@ApiOperation("【BU】【Token】取消转单")
	@PostMapping(Constants.PATH_VARIABLES_BUCODE+"/cancelCrossOrder")
	@Deprecated
	public BaseResponse cancelCrossOrder(@PathVariable(name = Constants.BUCODE) String productLineCode,@RequestBody CancelCrossOrderReq cancelCrossOrderReq){
		crossLabService.cancelCrossOrder(cancelCrossOrderReq);
		String logmsg = "Cancel Tops "+ Strings.nullToEmpty(cancelCrossOrderReq.getTops())+", Reason:"+ Strings.nullToEmpty(cancelCrossOrderReq.getReason());
		if(StringUtils.isNotBlank(cancelCrossOrderReq.getRemark())){
			logmsg += ","+cancelCrossOrderReq.getRemark();
		}
		BizLogHelper.setValue(cancelCrossOrderReq.getOrderNo(), logmsg, cancelCrossOrderReq.getSgsToken());
		return BaseResponse.newSuccessInstance("success");
	}

	@PostMapping(Constants.PATH_VARIABLES_BUCODE+"/updateTrfStatus")
	@ApiOperation("【BU】【Token】updateTrfStatus")
	public BaseResponse updateTrfStatus(@RequestBody UpdateTrfStatusReq updateTrfStatusReq){
		return  BaseResponse.newSuccessInstance(orderService.updateTrfStatus(updateTrfStatusReq));
	}

	/**
	 *
	 * @param order
	 * @return
	 */
	@PostMapping(Constants.PATH_VARIABLES_BUCODE+"/saveOrderInfo")
	@ApiOperation("【BU】【Token】保存订单信息")
	public BaseResponse<OrderDetailRsp> saveOrderInfo(@PathVariable(name = Constants.BUCODE) String productLineCode,@RequestBody OrderDetailDto order){
		order.setProductLineCode(productLineCode);
		order.setToTest(false);
		return BaseResponse.newSuccessInstance(orderDetailService.saveOrderInfoToTest(order));
	}
	@PostMapping(Constants.PATH_VARIABLES_BUCODE+"/beforeSaveOrderInfo")
	@ApiOperation("【BU】【Token】保存订单信息")
	public BaseResponse<List<TestLineLabSectionRsp>> beforeSaveOrderInfo(@PathVariable(name = Constants.BUCODE) String productLineCode, @RequestBody OrderDetailDto order){
		order.setProductLineCode(productLineCode);
		BaseResponse<List<TestLineLabSectionRsp>> baseResponse = orderDetailService.beforeSaveOrderInfo(order);
		return baseResponse;
	}

	@PostMapping(Constants.PATH_VARIABLES_BUCODE+"/deleteProductSample")
	@ApiOperation("【BU】【Token】删除样品信息")
	public @ResponseBody BaseResponse deleteProductSample(@PathVariable(name = Constants.BUCODE) String productLineCode,@RequestBody DeleteProductSampleReq deleteProductSampleReq) {
		return BaseResponse.newSuccessInstance(orderDetailService.deleteProductSampleById(deleteProductSampleReq));
	}


	@GetMapping(Constants.PATH_VARIABLES_BUCODE+"/status/list/{orderNo}")
	@ApiOperation("【BU】【Token】查询订单状态")
	public BaseResponse<StatusDTO> queryOrderStatus(@PathVariable(name = Constants.BUCODE) String productLineCode,@PathVariable(name="orderNo") String orderNo) {
		// 定义公共返回对象
		BaseResponse<StatusDTO> result = new BaseResponse<StatusDTO>();
		// 入参校验
		if (StringUtils.isNotBlank(orderNo)) {
			// 调用
			StatusDTO statusDTO=statusService.getStatusByObjectNo(orderNo,"order");
			// 结果设置
			result.setStatus(200);
			result.setData(statusDTO);
			// 如果查询不到结果则设置为New 状态返回
			if (Objects.isNull(statusDTO)) {
				StatusDTO defaultStatusPO = new StatusDTO();
				defaultStatusPO.setOldStatus(OrderStatus.New.getStatus());
				result.setData(defaultStatusPO);
			}
		} else {
			result.setStatus(500);
		}
		// 返回
		return result;
	}

	@GetMapping(Constants.PATH_VARIABLES_BUCODE+"/barCode/{orderNo}")
	@ApiOperation("【BU】【Token】查询订单条码")
	public Object barCode(@PathVariable(name = Constants.BUCODE) String productLineCode,@PathVariable(name="orderNo") String orderNo) throws Exception {
		String barCodeBase64 = barCodeTool.getBarCodeBase64(orderNo);
		return barCodeBase64;
	}


	/**
	 *
	 * @return
	 */
	@PostMapping(Constants.PATH_VARIABLES_BUCODE+"/updateTestFlag")
	@ApiOperation(value = "【BU】【Token】更新test Flag", notes = "更新test Flag")
	public BaseResponse<OrderDetailDto> updateTestFlag(@PathVariable(name = Constants.BUCODE) String productLineCode,@RequestBody UpdateTestFlagDTO updateTestFlagDTO){
		UserInfo user = SecurityUtil.getUser();
		BaseResponse result = new BaseResponse<StatusDTO>();
		if (user == null) {
			result.setStatus(500);
			result.setMessage("Get User Fail!");
			return result;
		}
		return BaseResponse.newSuccessInstance(orderService.updateTestFlag(updateTestFlagDTO.getOrderNo(),updateTestFlagDTO.getToTestFlag()));
	}



    @RequestMapping(value = "/checkChildOrderApproveStatus",method = RequestMethod.POST)
	@ApiOperation(value = "【BU】【Token】检查子单审批状态")
    public BaseResponse checkChildOrderApproveStatus(@RequestBody BossIdsReq bossIdsReq){
        return orderService.checkChildOrderApproveStatus(bossIdsReq);
    }

    @RequestMapping(value = "/getOrderSimpleInfo",method = RequestMethod.POST)
	@ApiOperation(value = "【BU】【Token】查询订单简要信息")
    public BaseResponse getOrderSimpleInfo(String orderNo){
        return orderService.getOrderSimpleInfo(orderNo);
    }



	@PostMapping(Constants.PATH_VARIABLES_BUCODE+"/batchUpdateOrder")
	@ApiOperation(value = "【BU】【Token】批量Update Order",notes = "批量Update Order")
	public BaseResponse batchUpdateOrder(@PathVariable(name = Constants.BUCODE) String productLineCode,@RequestBody BatchUpdateOrderDTO req){
		return orderService.batchUpdateOrder(req);
	}

	// update expect due date
	@PostMapping("/updateExpectDueDate")
	@ApiOperation(value = "【BU】【Token】updateExpectDueDate")
	public @ResponseBody BaseResponse updateExpectDueDate(@RequestBody UpdateExpectDueDateReq updateExpectDueDateReq) {
		return BaseResponse.newSuccessInstance(orderService.updateExpectDueDate(updateExpectDueDateReq)>0);
	}

	// reviseOrder
	@PostMapping("/reviseOrder")
	@ApiOperation(value = "【BU】【Token】reviseOrder")
	public @ResponseBody BaseResponse reviseOrder(@RequestBody ReviseOrderReq reviseOrderReq) {
		return BaseResponse.newSuccessInstance(orderService.reviseOrder(reviseOrderReq));
	}

	/**
	 * @param productLineCode
	 * @param orderNoList
	 * @return
	 */
	@PostMapping(Constants.PATH_VARIABLES_BUCODE+"/queryOrderDetailByOrderNoList")
	@ApiOperation(value = "【BU】【Token】根据OrderNoList批量查询orderDetail信息", notes = "根据OrderNoList批量查询orderDetail信息")
	public BaseResponse<OrderDetailDto> queryOrderDetailByOrderNoList(@PathVariable(name = Constants.BUCODE) String productLineCode, @RequestBody List<String> orderNoList){
		return BaseResponse.newSuccessInstance(orderDetailService.queryOrderDetailByOrderNoList(orderNoList));
	}

	/**
	 * 查询当前订单group订单
	 */
	@GetMapping(Constants.PATH_VARIABLES_BUCODE+"/group/{orderId}")
	@ApiOperation(value = "【BU】【Token】根据OrderN查询orderGroup", notes = "根据OrderN查询orderGroup")
	public BaseResponse<OrderGroupSearchRsp> getOrderListByGroup(@PathVariable(name = Constants.BUCODE) String productLineCode,@PathVariable(name = "orderId") String orderId){
		if(Func.isEmpty(productLineCode) || "null".equalsIgnoreCase(productLineCode)){
			return BaseResponse.newFailInstance("查询订单分组失败，当前BU不能为空!");
		}else {
			ProductLineContextHolder.setProductLineCode(productLineCode);
		}
		return orderDetailService.getOrderListByGroup(orderId);
	}

	/**
	 * toBoss操作校验
	 * @param toBossReq
	 * @return
	 */
	@RequestMapping(value = "/to/boss/check",method = RequestMethod.POST)
	@ApiOperation(value ="【BU】【Token】toBossCheck")
	public BaseResponse toBossCheck(@RequestBody ToBossReq toBossReq){
		return orderService.toBossCheck(toBossReq);
	}

	/**
	 * Confirm Order
	 */
	@RequestMapping(value = "/confirm",method = RequestMethod.POST)
	@ApiOperation(value ="【BU】【Token】confirm")
	public BaseResponse confirm(@RequestBody ConfirmOrderReq confirmOrderReq){
		return BaseResponse.newSuccessInstance(orderDetailService.confirmOrder(confirmOrderReq));
	}
	@RequestMapping(value = "/confirmToTest",method = RequestMethod.POST)
	@ApiOperation(value ="【BU】【Token】confirmToTest")
	public BaseResponse confirmToTest(@RequestBody ConfirmOrderReq confirmOrderReq){
		if(Func.isEmpty(confirmOrderReq)){
			return BaseResponse.newFailInstance("请求参数错误");
		}
		return ProductLineServiceHolder.getProductLineService(IOrderOperationService.class).toTestForOrder(confirmOrderReq.getOrder());
	}
	/**
	 * Confirm Order Check
	 */
	@RequestMapping(value = "/confirm/check",method = RequestMethod.POST)
	public BaseResponse confirmOrderCheck(@RequestBody ConfirmOrderReq confirmOrderReq){
		return orderDetailService.checkReportTemplate(confirmOrderReq);
	}
	/**
	 * 查询Order下对象的expectDueDate
	 */
	@RequestMapping(value = "/getAllDueDate",method = RequestMethod.POST)
	@ApiOperation(value ="【BU】【Token】查询Order下对象的expectDueDate")
	public BaseResponse<OrderExpectDueDateDTO> getAllExpectDueDate(@RequestBody ExpectDueDateReq req){
		return expectDueDateService.getAllExpectDueDate(req);
	}
	/**
	 * 取消操作更新Order下对象的expectDueDate
	 */
	@RequestMapping(value = "/cancelUpdateAllDueDate",method = RequestMethod.POST)
	@ApiOperation(value ="【BU】【Token】取消操作更新Order下对象的expectDueDate")
	public BaseResponse cancelUpdateAllDueDate(@RequestBody OrderExpectDueDateDTO req){
		return expectDueDateService.cancelUpdateAllDueDate(req);
	}

	/**
	 * 更新Order下对象的expectDueDate
	 */
	@RequestMapping(value = "/updateAllDueDate",method = RequestMethod.POST)
	@ApiOperation(value ="【BU】【Token】更新Order下对象的expectDueDate")
	public BaseResponse updateAllDueDate(@RequestBody OrderExpectDueDateDTO req){
		return expectDueDateService.updateAllDueDate(req);
	}

	/**
	 * 更新Order下对象的expectDueDate
	 */
	@RequestMapping(value = "/saveAllOrderDueDate",method = RequestMethod.POST)
	@ApiOperation(value ="【BU】【Token】更新Order下对象的expectDueDate")
	public BaseResponse saveAllOrderDueDate(@RequestBody BatchExpectDueDateReq batchExpectDueDateReq){
		return expectDueDateService.batchUpdateAllDueDate(batchExpectDueDateReq);
	}

	/**
	 * 根据TL计算Order/Report/Job DueDate
	 */
	@RequestMapping(value = "/calculateDueDate",method = RequestMethod.POST)
	@ApiOperation(value ="【BU】【Token】根据TL计算Order/Report/Job DueDate")
	public BaseResponse<OrderExpectDueDateDTO> calculateDueDateByTl(@RequestBody OrderExpectDueDateDTO req){
		return expectDueDateService.calculateDueDateByTl(req);
	}

	/**
	 * 查询组件buParam
	 * @param buParamReq
	 * @return
	 */
	@RequestMapping(value = "/bu/param",method = RequestMethod.POST)
	@ApiOperation(value ="【无拦截】查询组件buParam")
	//@AccessRule(needProductLine = false,needToken = false)
	public BaseResponse getBuParam(@RequestBody BuParamReq buParamReq){
		return orderService.getBuParam(buParamReq);
	}

	/**
	 * 查询订单下hardCopyInfo
	 * @param orderNo
	 * @return
	 */
	@RequestMapping(value = "/hard/copy/info/{orderNo}",method = RequestMethod.GET)
	@ApiOperation(value ="【BU】【Token】getHardCopyInfo")
	public BaseResponse getHardCopyInfo(@PathVariable String orderNo){
		return orderService.getHardCopyInfo(orderNo);
	}

	/**
	 * hardCopy修改
	 * @param hardCopyEditReq
	 * @return
	 */
	@RequestMapping(value = "/hard/copy/edit",method = RequestMethod.POST)
	@ApiOperation(value ="【BU】【Token】hardCopyEdit")
	public BaseResponse hardCopyEdit(@RequestBody HardCopyEditReq hardCopyEditReq){
		return orderService.hardCopyEdit(hardCopyEditReq);
	}

	/**
	 * 查询CoverPage Todo List
	 * @return
	 */
	@RequestMapping(value = "/coverPage/list",method = RequestMethod.POST)
	@ApiOperation(value ="【BU】【Token】coverPageList")
	public BaseResponse<PageInfo<OrderListDto>> coverPageList(@RequestBody QueryCoverPageListReq req){
		return orderService.getCoverPageList(req);
	}

	/**
	 * changeCustomer
	 * @param toBossReq
	 * @return
	 */
	@RequestMapping(value = "/customer/updateRelationObject",method = RequestMethod.POST)
	@ApiOperation(value ="【BU】【Token】updateRelationObject")
	public BaseResponse updateRelationObject(@RequestBody UpdateRelationshipObjectReq toBossReq){
		boolean isRet=orderService.updateRelationObject(toBossReq);
		return BaseResponse.newSuccessInstance(isRet);
	}

	/**
	 * renew时查询sample信息
	 * @param orderNo
	 * @return
	 */
	@RequestMapping(value = "/sample/renew/{orderNo}/{reportId}",method = RequestMethod.GET)
	@ApiOperation(value = "【BU】【Token】renew时查询sample信息",notes = "renew时查询sample信息")
	BaseResponse getSampleInfoForRenew(@PathVariable String orderNo,@PathVariable String reportId){
		return orderDetailService.getSampleInfoForRenew(orderNo,reportId);
	}

	@RequestMapping(value = "/update/attachment",method = RequestMethod.GET)
	@ApiOperation(value ="【BU】【Token】updateAttachment")
	public BaseResponse updateAttachment(){
		return orderService.updateAttachment();
	}

	/**
	 * 删除sample时校验是否关联图片
	 * @param orderId
	 * @param sampleNo
	 * @return
	 */
	@RequestMapping(value = "/sample/delete/check/{orderId}/{sampleNo}",method = RequestMethod.GET)
	@ApiOperation(value = "【BU】【Token】删除sample时校验是否关联图片",notes = "删除sample时校验是否关联图片")
	BaseResponse deleteSampleCheck(@PathVariable String orderId,@PathVariable String sampleNo){
		return orderService.deleteSampleCheck(orderId,sampleNo);
	}


	/**
	 * 操作订单中按钮时记录操作日志
	 * @param orderId
	 * @param button
	 * @return
	 */
	@RequestMapping(value = "/operation/button/log/{orderId}/{button}",method = RequestMethod.GET)
	@ApiOperation(value = "【BU】【Token】操作订单中按钮时记录操作日志",notes = "操作订单中按钮时记录操作日志")
	BaseResponse saveButtonLog(@PathVariable String orderId,@PathVariable String button){
		return orderService.saveButtonLog(orderId,button);
	}


	/**
	 * charge order生成订单号
	 * @param generateChargeOrderNoReq
	 * @return
	 */
	@RequestMapping(value = "/generate/charge/order/no",method = RequestMethod.POST)
	@ApiOperation(value ="【BU】【Token】GenerateChargeOrderNo")
	BaseResponse generateChargeOrderNo(@RequestBody GenerateChargeOrderNoReq generateChargeOrderNoReq){
		return orderService.generateOrderNo(generateChargeOrderNoReq);
	}

	/**
	 * 是否为amend产生的订单
	 * @param orderNo
	 * @return
	 */
	@RequestMapping(value = "/is/amend/{orderNo}",method = RequestMethod.GET)
	@ApiOperation(value ="【BU】【Token】是否为amend产生的订单")
	BaseResponse isAmendOrder(@PathVariable String orderNo){
		return orderService.isAmendOrder(orderNo);
	}

	@PostMapping(value = "/calcMainCurrencyFee")
	@ApiOperation(value ="【BU】【Token】calcMainCurrencyFee")
	public BaseResponse<MainCurrencyFeeRsp> calcMainCurrencyFee(@RequestBody GetMainCurrencyFeeReq getMainCurrencyFeeReq) {
		return orderService.getMainCurrencyFee(getMainCurrencyFeeReq);
	}

	/**
	 * 获取order payment status
	 * @param orderId
	 * @return
	 */
	@RequestMapping(value = "/payment/status/{orderId}",method = RequestMethod.GET)
	@ApiOperation(value ="【BU】【Token】获取order payment status")
	@AccessRule
	public BaseResponse getPaymentStatus(@PathVariable String orderId){
		return orderService.getPaymentStatus(orderId);
	}

	/**
	 * 页面更新paymentStatus
	 * @param updatePaymentStatusReq
	 * @return
	 */
	@RequestMapping(value = "/payment/status/update",method = RequestMethod.POST)
	@ApiOperation(value ="【BU】【Token】页面更新paymentStatus")
	@AccessRule
	public BaseResponse paymentStatusUpdate(@RequestBody UpdatePaymentStatusReq updatePaymentStatusReq){
		return orderService.updatePaymentStatus(updatePaymentStatusReq);
	}

	/**
	 * 订单取消前的校验
	 * @param orderId
	 * @return
	 */
	@RequestMapping(value = "/cancel/check/{orderId}",method = RequestMethod.GET)
	@ApiOperation(value ="【BU】【Token】订单取消前的校验")
	public BaseResponse cancelCheck(@PathVariable String orderId){
		return orderDetailService.orderCancelCheck(orderId);
	}

	/**
	 * 订单cancel  发送审批
	 * @param orderCancelReq
	 * @return
	 */
	@RequestMapping(value = "/cancel/apply",method = RequestMethod.POST)
	@ApiOperation(value ="【BU】【Token】订单cancel  发送审批")
	public BaseResponse cancelApply(@RequestBody OrderCancelReq orderCancelReq){
		return orderDetailService.cancelApply(orderCancelReq);
	}


	@RequestMapping(value = "/paid/update/pay/status/{buCode}",method = RequestMethod.GET)
	@ApiOperation(value ="【BU】【Token】订单cancel  updatePayStatusByPaid")
	public BaseResponse updatePayStatusByPaid(@PathVariable String buCode){
		ProductLineContextHolder.setProductLineCode(buCode);
		return orderService.updatePayStatusByPaid();
	}

	@PostMapping(value = "/cancelSample")
	@ApiOperation(value ="【BU】【Token】cancelSample")
	public BaseResponse cancelSample(@RequestBody CancelSampleReq reqObject) {
		return orderService.cancelSample(reqObject);
	}


	/**
	 *
	 */
	@PostMapping("/sendQuotationToCustomer")
	@ApiOperation(value = "【BU】【Token】Quotation报价单发送给客户", notes = "Quotation报价单发送给客户")
	public BaseResponse<Boolean> sendQuotationToCustomer(@RequestBody QuotationSendToCustomerReq sendReq) {
		return orderDetailService.sendQuotationToCustomer(sendReq);
	}

	@PostMapping("/checkSendQuotationToCustomer")
	@ApiOperation(value = "【BU】【Token】Quotation报价单发送给客户", notes = "Quotation报价单发送给客户")
	public BaseResponse<Boolean> checkSendQuotationToCustomer(@RequestBody QuotationSendToCustomerReq sendReq) {
		return orderDetailService.checkSendQuotationToCustomer(sendReq);
	}

	/**
	 *
	 */
	@PostMapping("/batchSendQuotationToCustomer")
	@ApiOperation(value = "【BU】【Token】Quotation报价单批量发送给客户", notes = "Quotation报价单批量发送给客户")
	public BaseResponse<Boolean> batchSendQuotationToCustomer(@RequestBody QuotationBatchSendToCustomerReq sendReq) {
		return orderDetailService.batchSendQuotationToCustomer(sendReq);
	}


	@GetMapping(value = "/toOrderPage")
	@ApiOperation(value = "【BU】【Token】建单路由")
	public void toOrderPage(HttpServletRequest request,
							HttpServletResponse response) {

		String orderId = request.getParameter("orderId");
		if(Func.isNotEmpty(orderId)){
			redirectOrderDetail(response, orderId);
		}else {
			//
			String refSystemId =  request.getParameter("refSystemId");
			String orderCreateMethod = resolveOrderCreateMethod(refSystemId);

			String baseUrl = StrUtil.format("/gpo-web/#/{}Info?",orderCreateMethod);
			StringBuilder urlBuilder = new StringBuilder(baseUrl);
			Map<String, String[]> parameterMap = request.getParameterMap();
			for (Map.Entry<String, String[]> paramEntry : parameterMap.entrySet()) {
				String paramName = paramEntry.getKey();
				String paramValue = ArrayUtil.get(paramEntry.getValue(), 0);
				if (Func.isNotEmpty(paramValue)) {
					urlBuilder.append("&").append(paramName).append("=").append(paramValue);
				}
			}
			int lastC = urlBuilder.lastIndexOf("&");
			if (lastC == (urlBuilder.length() - 1)) {
				urlBuilder.deleteCharAt(lastC);
			}
			sendRedirect(response, urlBuilder.toString());
		}
	}

	@PostMapping(value = "/createOrderPage")
	@ApiOperation(value = "【BU】【Token】建单路由")
	public BaseResponse toOrderPage(@RequestBody SciToOrderDetailReq sciToOrderDetailReq) {
		String refSystemId = sciToOrderDetailReq.getRefSystemId();
		String orderCreateMethod = resolveOrderCreateMethod(refSystemId);

		String baseUrl = StrUtil.format("/gpo-web/#/{}Info?",orderCreateMethod);
		StringBuilder urlBuilder = new StringBuilder(baseUrl);
		urlBuilder.append("refSystemId=").append(refSystemId);
		List<SciToOrderDetailReq.SciToOrderDetailTrfReq> reqTrfInfos = sciToOrderDetailReq.getTrfNo();
		List<Map<String,String>> trfNoList = new ArrayList<>();
		String trfNoStr = "";
		GetRefNoReq getRefNoReq = new GetRefNoReq();
		getRefNoReq.setRefSystemId(Func.toInteger(refSystemId));
		getRefNoReq.setProductLineCode(sciToOrderDetailReq.getProductLineCode());
		List<TrfInfoReq> trfInfos = new ArrayList<>();
		if(Func.isNotEmpty(reqTrfInfos)){
			trfNoStr = reqTrfInfos.get(0).getTrfNo();
			reqTrfInfos.forEach(trfInfo->{
				String trfNo = trfInfo.getTrfNo();
				String sampleNo = trfInfo.getSampleNo();
				List<String> testSampleIds = trfInfo.getTestSampleIds();
				List<String> pictureIds = trfInfo.getPictureIds();
				TrfInfoReq trfInfoReq = new TrfInfoReq();
				trfInfoReq.setTrfNo(trfNo);
				trfInfoReq.setSampleNo(sampleNo);
				trfInfoReq.setTestSampleIds(testSampleIds);
				trfInfoReq.setPictureIds(pictureIds);
				trfInfos.add(trfInfoReq);
			});
			Set<String> trfNoSets = reqTrfInfos.stream().map(SciToOrderDetailReq.SciToOrderDetailTrfReq::getTrfNo).collect(Collectors.toSet());
			trfNoSets.forEach(trfNo->{
				Map<String,String> trfMap = new HashMap<>();
				trfMap.put("trfNo",trfNo);
				trfNoList.add(trfMap);
			});
			urlBuilder.append("&trfNo=").append(Func.toJson(trfNoList));
		}
		getRefNoReq.setTrfInfos(trfInfos);

		String productLineCode = ProductLineContextHolder.getProductLineCode();
		if(Func.isNotEmpty(productLineCode)){
			urlBuilder.append("&productLineCode=").append(productLineCode);
		}
		String trfRequestKey = com.sgs.preorder.core.util.Constants.REDIS_KEY_PREFIX_PREORDER + "trfCreateKey_"+trfNoStr;

		redisUtil.set(trfRequestKey, Func.toJson(getRefNoReq), 3600L);

		urlBuilder.append("&trfRequestKey=").append(trfRequestKey);
		int lastC = urlBuilder.lastIndexOf("&");
		if (lastC == (urlBuilder.length() - 1)) {
			urlBuilder.deleteCharAt(lastC);
		}
		Map<String, Object> resultMap = new HashMap<>();
		resultMap.put("redirectUrl",urlBuilder.toString());
		return BaseResponse.newSuccessInstance(resultMap);
//		sendRedirect(response, urlBuilder.toString());
	}

	private String resolveOrderCreateMethod(String refSystemId) {
		String orderCreateMethod = "enquiry";
		String productLineCode = ProductLineContextHolder.getProductLineCode();
		BuParamReq buParamReq = new BuParamReq();
		buParamReq.setProductLineCode(productLineCode);
		buParamReq.setGroupCode(com.sgs.preorder.core.util.Constants.BU_PARAM.ORDER.GROUP);
		buParamReq.setParamCode(com.sgs.preorder.core.util.Constants.BU_PARAM.ORDER.CREATE_ORDER_METHOD.CODE);
		List<OrderCreateMethodBuParamsDto> orderCreateMethodBuParams = frameWorkClient.getBuParam(buParamReq,new TypeReference<List<OrderCreateMethodBuParamsDto>>(){});
		if(Func.isEmpty(orderCreateMethodBuParams)){
			return orderCreateMethod;
		}

		for (OrderCreateMethodBuParamsDto orderCreateMethodBuParam : orderCreateMethodBuParams) {
			if(Func.equalsSafe(refSystemId,Func.toStr(orderCreateMethodBuParam.getRefSystemId()))){
				orderCreateMethod = orderCreateMethodBuParam.getOrderMethod();
				break;
			}
		}
		return orderCreateMethod;

	}

	private void redirectOrderDetail(HttpServletResponse response, String orderId) {
		OrderIdGetReq idGetReq = new OrderIdGetReq();
		idGetReq.setOrderId(orderId);
		SimpleOrderDto simpleOrder  =  standardOrderService.getOrderById(idGetReq);
		StringBuilder urlBuilder ;
		if(Func.isNotEmpty(simpleOrder)){
			urlBuilder = new StringBuilder("/gpo-web/#/orderInfo/");
			urlBuilder.append(simpleOrder.getId());
			urlBuilder.append("?id=").append(simpleOrder.getId());
		}else{
			simpleOrder  =  standardOrderService.getEnquiryOrderById(idGetReq);
			urlBuilder = new StringBuilder("/gpo-web/#/enquiryInfo/");
			urlBuilder.append(simpleOrder.getEnquiryId());
			urlBuilder.append("?id=").append(simpleOrder.getEnquiryId());
		}
		sendRedirect(response,urlBuilder.toString());
	}


	private void sendRedirect(HttpServletResponse response,String url){
		try {
			response.sendRedirect(url);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	@RequestMapping(value = "/resolveClosedOrder",method = RequestMethod.GET)
	public BaseResponse resolveClosedOrder() {
		BaseResponse baseResponse = new BaseResponse();
		List<String> productLines = onlineConfig.getProductLines();
		productLines.forEach(BUCode->{
			if(Func.isNotEmpty(BUCode)){
				ProductLineContextHolder.setProductLineCode(BUCode);
				orderService.getResolveCloseOrder(BUCode);
			}
		});
		baseResponse.setStatus(200);
		baseResponse.setMessage("遍历线上BUCode，把未关闭的订单关闭的操作完成");
		return baseResponse;
	}

	@RequestMapping(value = "/errorComeHere",method = RequestMethod.GET)
	public BaseResponse errorComeHere() {
		BaseResponse baseResponse = new BaseResponse();
		baseResponse.setStatus(200);
		baseResponse.setMessage("遍历线上BUCode，把未关闭的订单关闭的操作完成");
		return baseResponse;
	}

	@RequestMapping(value = "/push-skip",method = RequestMethod.POST)
	@AccessRule(needToken = false,needProductLine = false)
	public BaseResponse pushSkipOrder(@RequestBody ConfirmOrderReq req) {
		return BaseResponse.newSuccessInstance(orderDetailService.pushSkipOrder(req));
	}

	@PostMapping(value = "/batchToSGSMart")
	@AccessRule
	public BaseResponse batchToSGSMart(@RequestBody BatchToSGSMartReq req){
		return BaseResponse.newSuccessInstance(orderService.batchToSGSMart(req));
	}

	@PostMapping(value ="/computeTatValue")
	public BaseResponse computeTatValue(@RequestBody ComputeTatReq req){
		return expectDueDateService.computeTatValue(req);
	}

	@RequestMapping(value = "/specialToSGSMart",method = RequestMethod.POST)
	@AccessRule(needProductLine = false)
	public BaseResponse specialToSGSMart(@RequestBody ConfirmOrderReq req) {
		return BaseResponse.newSuccessInstance(orderDetailService.specialToSGSMart(req));
	}
}
