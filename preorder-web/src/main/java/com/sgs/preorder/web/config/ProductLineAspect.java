package com.sgs.preorder.web.config;

import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.LabCodeContextHolder;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.preorder.core.constants.Constants;
import com.sgs.preorder.facade.model.info.user.UserLabBuInfo;
import com.sgs.preorder.integration.client.UserClient;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.HandlerMapping;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Map;

/**
 * DataSource context aspect
 * Created by Kevin on 2020/7/14.
 */

@Aspect
@Component
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class ProductLineAspect {
    private static Logger logger = LoggerFactory.getLogger(ProductLineAspect.class);
    private static String PRODUCTLINECODE_METHOD="ProductLineCode";
    private static String LOCATIONCODE_METHOD="LocationCode";
    private static String LABCODE_METHOD="LabCode";
    private static String SGSTOKEN_METHOD="SgsToken";
    private static String TOKEN_METHOD="Token";


    @Autowired
    private UserClient userClient;
    /**
     *对入口controller,facade,kafka  todo webservice
     * 进行拦截获取productlineCode
     */
    @Pointcut("execution(* com.sgs.preorder.web.controllers..*.*(..))||execution(* com.sgs.preorder.facade.*Facade.*(*))||execution(* com.sgs.preorder.domain.kafka..*.*(..))")
    public void aspect() {

    }

    /**
     *对入口com.sgs.otsnotes.facade拦截
     * 进行拦截获取productlineCode
     */
    @Pointcut("execution(* com.sgs.otsnotes.facade.*Facade.*(*))")
    public void aspectForGpn() {

    }


    /**
     *
     * @param point
     */
    @Before("aspect()")
    public void before(JoinPoint point) {
        try {
            //bucode
            String buCodeContext=getValueCode(Constants.BUCODE,point);
            //location
            String locationCode=getValueCode(Constants.LOCATIONCODE,point);
            //labCode
            String labCode=getValueCode(Constants.LABCODE,point);
            //sgsToken
            String sgsToken=getValueCode(Constants.SGSTOKEN,point);
            if(Func.isEmpty(sgsToken)){
                sgsToken=getValueCode(Constants.TOKEN,point);
            }
            if(Func.isEmpty(labCode)&&Func.isNotEmpty(sgsToken)){
                UserLabBuInfo userLabBuInfo = userClient.getUserLabBuInfo(sgsToken);
                if(Func.isNotEmpty(userLabBuInfo)){
                    labCode=userLabBuInfo.getLabCode();
                }
            }
            if(Func.isNotEmpty(labCode)){
                LabCodeContextHolder.setLabCode(labCode);
            }else{
                labCode = SystemContextHolder.getLabCode();
                LabCodeContextHolder.setLabCode(labCode);
            }
            // set bu
            logger.debug("Controller Current Environment BuCode:{},LocationCode:{},LabCode:{}",buCodeContext,locationCode,labCode);
            ProductLineContextHolder.setProductLineCode(buCodeContext,null,locationCode);
        } catch (Exception ex) {
            logger.error("DataSourceContextAspect.before Error：{}", ex);
        }
    }

    /**
     *
     * @param point
     */
    @Before("aspectForGpn()")
    public void aspectForGpn(JoinPoint point) {
        try {
            //call gpo
            this.setValueForProductLineCode(Constants.BUCODE,point.getArgs(),ProductLineContextHolder.getProductLineCode());
        } catch (Exception ex) {
            logger.error("DataSourceContextAspect.before Error：{}", ex);
        }
    }

    private void setValueForProductLineCode(String valueType,Object[] params,Object value) {
        if(params==null){
            return;
        }
        for (Object obj:params){
            if(obj==null){
                continue;
            }
            Class objClazz = (Class) obj.getClass();
            try {
                if(Constants.BUCODE.equals(valueType)){
                    Method method_get= Arrays.asList(objClazz.getMethods()).stream().filter(e->e.getName().equals("get"+PRODUCTLINECODE_METHOD)).findFirst().orElse(null);
                    if(method_get!=null){
                        Object objFieldValue=method_get.invoke(obj);
                        if(Func.isEmpty(objFieldValue)){
                            Method method_set= Arrays.asList(objClazz.getMethods()).stream().filter(e->e.getName().equals("set"+PRODUCTLINECODE_METHOD)).findFirst().orElse(null);
                            if(method_set!=null){
                                method_set.invoke(obj,value);
                            }
                        }
                    }
                }
            }catch (Exception e) {
                logger.error(e.getMessage(),e);
            }
        }
    }


    private String getValueCode(String valueType,JoinPoint point) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        String valueCode ="";
        if (requestAttributes != null) {
            ServletRequestAttributes sra = (ServletRequestAttributes)requestAttributes;
            if (sra != null) {
                HttpServletRequest request=sra.getRequest();
                //get value from path variable
                Map map=(Map)request.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
                if(map!=null){
                    valueCode=(String)map.get(valueType);
                }
                //get value from parameter
                if(StringUtils.isEmpty(valueCode)){
                    valueCode = request.getParameter(valueType);
                }
                //get value from head
                if(StringUtils.isEmpty(valueCode)){
                    valueCode = request.getHeader(valueType);
                }
            }
        }

        //get bu or location from object
        if(StringUtils.isEmpty(valueCode)){
            valueCode=this.getValueCodeByObject(valueType,point.getArgs());
        }
        return valueCode;
    }


    private String getValueCodeByObject(String valueType,Object[] params) {
        if(params==null){
            return "";
        }
        for (Object obj:params){
            if(obj==null){
                continue;
            }
            Class objClazz = (Class) obj.getClass();
            try {
                Method method=null;
                if(Constants.BUCODE.equals(valueType)){
                    method= Arrays.asList(objClazz.getMethods()).stream().filter(e->e.getName().equals("get"+PRODUCTLINECODE_METHOD)).findFirst().orElse(null);
                }else if(Constants.LOCATIONCODE.equals(valueType)){
                    method=Arrays.asList(objClazz.getMethods()).stream().filter(e->e.getName().equals("get"+LOCATIONCODE_METHOD)).findFirst().orElse(null);
                }else if(Constants.LABCODE.equals(valueType)){
                    method=Arrays.asList(objClazz.getMethods()).stream().filter(e->e.getName().equals("get"+LABCODE_METHOD)).findFirst().orElse(null);
                }else if(Constants.SGSTOKEN.equals(valueType)){
                    method=Arrays.asList(objClazz.getMethods()).stream().filter(e->e.getName().equals("get"+SGSTOKEN_METHOD)).findFirst().orElse(null);
                }else if(Constants.TOKEN.equals(valueType)){
                    method=Arrays.asList(objClazz.getMethods()).stream().filter(e->e.getName().equals("get"+TOKEN_METHOD)).findFirst().orElse(null);
                }
                if(method!=null){
                    Object objFieldValue=method.invoke(obj);
                    return objFieldValue==null?"":objFieldValue.toString();
                }

            }catch (Exception e) {
                logger.error(e.getMessage(),e);
            }
        }
        return "";
    }
    /**
     *
     * @param point
     */
    @After("aspect()")
    public void after(JoinPoint point) {
        ProductLineContextHolder.clear();
        LabCodeContextHolder.clear();
    }
}
