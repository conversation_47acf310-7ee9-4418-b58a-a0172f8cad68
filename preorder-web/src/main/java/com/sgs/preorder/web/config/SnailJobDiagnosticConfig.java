package com.sgs.preorder.web.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * SnailJob诊断配置类
 * 
 * 主要功能：
 * - 诊断SnailJob客户端配置和连接问题
 * - 提供详细的启动日志和状态监控
 * - 帮助定位注册失败的原因
 * 
 * <AUTHOR>
 * @since 1.1.566
 */
@Slf4j
@Component
@Order(1000) // 确保在其他组件之后执行
public class SnailJobDiagnosticConfig implements ApplicationRunner {

    @Value("${snail-job.server.host:}")
    private String serverHost;

    @Value("${snail-job.server.port:0}")
    private Integer serverPort;

    @Value("${snail-job.namespace:}")
    private String namespace;

    @Value("${snail-job.group:}")
    private String group;

    @Value("${snail-job.token:}")
    private String token;

    @Value("${snail-job.enabled:false}")
    private Boolean enabled;

    @Value("${snail-job.register-interval:30}")
    private Integer registerInterval;

    @Value("${snail-job.heartbeat-interval:10}")
    private Integer heartbeatInterval;

    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("🔍 ===== SnailJob诊断开始 =====");
        
        // 1. 配置信息诊断
        diagnoseConfiguration();
        
        // 2. 网络连接诊断
        diagnoseNetworkConnection();
        
        // 3. Bean加载诊断
        diagnoseBeanLoading();
        
        // 4. 启动监控
        if (enabled) {
            startDiagnosticMonitoring();
        }
        
        log.info("🔍 ===== SnailJob诊断完成 =====");
    }

    /**
     * 配置信息诊断
     */
    private void diagnoseConfiguration() {
        // 0. Protobuf版本诊断
        log.info("🔧 Protobuf版本诊断:");
        try {
            String protobufVersion = com.google.protobuf.GeneratedMessageV3.class.getPackage().getImplementationVersion();
            log.info("  Protobuf版本: {}", protobufVersion != null ? protobufVersion : "未知");

            // 测试关键方法是否存在
            try {
                com.google.protobuf.GeneratedMessageV3.class.getDeclaredMethod("isStringEmpty", Object.class);
                log.info("✅ Protobuf版本兼容 - isStringEmpty方法存在");
            } catch (NoSuchMethodException e) {
                log.error("❌ Protobuf版本不兼容 - isStringEmpty方法不存在，需要升级protobuf版本到3.25+");
            }
        } catch (Exception e) {
            log.error("❌ Protobuf版本检查失败: {}", e.getMessage());
        }

        log.info("📋 配置信息诊断:");
        log.info("  服务端地址: {}:{}", serverHost, serverPort);
        log.info("  命名空间: {}", namespace);
        log.info("  组名称: {}", group);
        log.info("  令牌: {}", token != null && !token.isEmpty() ? "已配置(长度:" + token.length() + ")" : "❌未配置");
        log.info("  是否启用: {}", enabled);
        log.info("  注册间隔: {}秒", registerInterval);
        log.info("  心跳间隔: {}秒", heartbeatInterval);

        // 配置验证
        boolean configValid = true;
        if (!enabled) {
            log.warn("⚠️  SnailJob已禁用");
            return;
        }
        
        if (serverHost == null || serverHost.trim().isEmpty()) {
            log.error("❌ server.host不能为空");
            configValid = false;
        }
        if (serverPort == null || serverPort <= 0) {
            log.error("❌ server.port必须大于0，当前值: {}", serverPort);
            configValid = false;
        }
        if (namespace == null || namespace.trim().isEmpty()) {
            log.error("❌ namespace不能为空");
            configValid = false;
        }
        if (group == null || group.trim().isEmpty()) {
            log.error("❌ group不能为空");
            configValid = false;
        }
        if (token == null || token.trim().isEmpty()) {
            log.error("❌ token不能为空");
            configValid = false;
        }
        
        if (configValid) {
            log.info("✅ 配置验证通过");
        } else {
            log.error("❌ 配置验证失败，这可能是注册失败的原因");
        }
    }

    /**
     * 网络连接诊断
     */
    private void diagnoseNetworkConnection() {
        if (!enabled) return;
        
        log.info("🌐 网络连接诊断:");
        try {
            Socket socket = new Socket();
            long startTime = System.currentTimeMillis();
            socket.connect(new InetSocketAddress(serverHost, serverPort), 5000);
            long endTime = System.currentTimeMillis();
            
            log.info("✅ 服务端连接成功: {}:{} (耗时: {}ms)", serverHost, serverPort, endTime - startTime);
            socket.close();
        } catch (Exception e) {
            log.error("❌ 服务端连接失败: {}:{}", serverHost, serverPort);
            log.error("   错误详情: {}", e.getMessage());
            log.error("   可能原因: 1)服务端未启动 2)网络不通 3)防火墙阻止 4)端口配置错误");
        }
    }

    /**
     * Bean加载诊断
     */
    private void diagnoseBeanLoading() {
        log.info("🔧 Bean加载诊断:");
        
        try {
            // 检查SnailJob自动配置类
            try {
                Class<?> autoConfigClass = Class.forName("com.aizuda.snailjob.client.starter.SnailJobAutoConfiguration");
                log.info("✅ SnailJob自动配置类已加载: {}", autoConfigClass.getName());
            } catch (ClassNotFoundException e) {
                log.error("❌ SnailJob自动配置类未找到，请检查依赖是否正确引入");
            }
            
            // 检查相关Bean
            String[] beanNames = applicationContext.getBeanNamesForType(Object.class);
            int snailJobBeanCount = 0;
            
            for (String beanName : beanNames) {
                if (beanName.toLowerCase().contains("snail") || 
                    (beanName.toLowerCase().contains("job") && !beanName.contains("dubbo"))) {
                    log.debug("  发现相关Bean: {}", beanName);
                    snailJobBeanCount++;
                }
            }
            
            log.info("✅ 检测到 {} 个SnailJob相关Bean", snailJobBeanCount);
            
            if (snailJobBeanCount == 0) {
                log.warn("⚠️  未检测到SnailJob相关Bean，可能自动配置未生效");
            }
            
        } catch (Exception e) {
            log.error("❌ Bean诊断时出错: {}", e.getMessage());
        }
    }

    /**
     * 启动诊断监控
     */
    private void startDiagnosticMonitoring() {
        ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "snail-job-diagnostic-monitor");
            t.setDaemon(true);
            return t;
        });
        
        // 延迟60秒后开始监控，给客户端足够时间完成注册
        executor.scheduleAtFixedRate(() -> {
            log.info("🔍 SnailJob状态监控:");
            log.info("   如果长时间没有看到注册成功和心跳日志，请检查:");
            log.info("   1. 服务端是否正确配置了namespace: {} 和 group: {}", namespace, group);
            log.info("   2. 服务端是否接受token: {}", token != null ? token.substring(0, Math.min(10, token.length())) + "..." : "null");
            log.info("   3. 服务端日志是否有相关错误信息");
            log.info("   4. 客户端和服务端版本是否兼容");
        }, 60, 120, TimeUnit.SECONDS); // 60秒后开始，每2分钟检查一次
    }
}
