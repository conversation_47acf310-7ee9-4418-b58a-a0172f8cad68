package com.sgs.preorder.web.config;

import com.alibaba.fastjson.JSON;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.log.SystemLogHelper;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.preorder.core.config.AdministratorsConfig;
import com.sgs.preorder.core.constants.Constants;
import com.sgs.preorder.facade.model.enums.SystemLogType;
import com.sgs.preorder.integration.client.TokenClient;
import com.sgs.preorder.integration.client.UserClient;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Component;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.HandlerMapping;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


@Aspect
@Component
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class ObjectSettingAspect {
    private static final Logger logger = LoggerFactory.getLogger(ObjectSettingAspect.class);

    @Autowired
    SystemLogHelper systemLogHelper;
    @Autowired
    AdministratorsConfig administrationConfig;
    @Autowired
    TokenClient tokenClient;

    private static final String[] INTERCEPT_URL_PATTERNS = {
            "/gpo-api/busetting/object/template/save",
            "/gpo-api/busetting/object/template/status/change",
    };

    private static String SGSTOKEN_METHOD="SgsToken";
    private static String TOKEN_METHOD="Token";

    @Pointcut("execution(* com.sgs.preorder.web.controllers..*(..))")
    public void objectSettingPointcut() {
    }

    /**
     * 在方法执行前拦截
     */
    @Before("objectSettingPointcut()")
    public void beforeMethod(JoinPoint joinPoint) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return;
        }

        HttpServletRequest request = attributes.getRequest();
        String requestUrl = request.getRequestURI();
        String regionAccount = "";
        //sgsToken
        String sgsToken = getValueCode(Constants.SGSTOKEN,joinPoint);
        if(Func.isEmpty(sgsToken)){
            sgsToken = getValueCode(Constants.TOKEN,joinPoint);
        }
        if(Func.isNotEmpty(sgsToken)){
            UserInfo userInfo = tokenClient.getUser(sgsToken);
            if(Func.isNotEmpty(userInfo)){
                regionAccount = userInfo.getRegionAccount();
            }
        }

        // 检查当前请求URL是否在需要拦截的列表中
        if (isUrlMatched(requestUrl)) {
            //记录SystemLog
            SystemLog systemLog = new SystemLog();
            systemLog.setUrl(requestUrl);
            systemLog.setObjectType("buSetting");
            systemLog.setObjectNo(request.getMethod());
            systemLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            systemLog.setType(SystemLogType.API.getType());
            systemLog.setRemark("请求IP: {}" + request.getRemoteAddr());
            systemLog.setRequest(JSON.toJSONString(joinPoint.getArgs()));
            systemLog.setOperationType("buSetting");
            systemLog.setCreateBy(regionAccount);
            systemLogHelper.save(systemLog);
            List<String> regionAccounts = administrationConfig.getRegionAccount();
            //判断操作人是否在超级管理员内
            if(Func.isNotEmpty(regionAccounts) && !regionAccounts.contains(regionAccount)){
                throw new BizException("当前用户没有权限操作，请联系管理员"+ Func.toStr(regionAccounts));
            }
        }
    }

    /**
     * 检查URL是否与拦截模式匹配
     */
    private boolean isUrlMatched(String url) {
        for (String pattern : INTERCEPT_URL_PATTERNS) {
            // 简单的模式匹配实现，可根据需要扩展
            if (matchPattern(url, pattern)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 模式匹配工具方法，支持*通配符
     */
    private boolean matchPattern(String url, String pattern) {
        // 将模式和URL按/分割
        String[] urlParts = url.split("/");
        String[] patternParts = pattern.split("/");
        // 如果部分数量不同，直接不匹配
        if (urlParts.length != patternParts.length) {
            return false;
        }

        // 逐个部分匹配
        for (int i = 0; i < urlParts.length; i++) {
            // 模式部分为*时匹配任意内容
            if (!"*".equals(patternParts[i]) && !urlParts[i].equals(patternParts[i])) {
                return false;
            }
        }
        return true;
    }
    private String getValueCode(String valueType,JoinPoint point) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        String valueCode ="";
        if (requestAttributes != null) {
            ServletRequestAttributes sra = (ServletRequestAttributes)requestAttributes;
            if (sra != null) {
                HttpServletRequest request=sra.getRequest();
                //get value from path variable
                Map map=(Map)request.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
                if(map!=null){
                    valueCode=(String)map.get(valueType);
                }
                //get value from parameter
                if(StringUtils.isEmpty(valueCode)){
                    valueCode = request.getParameter(valueType);
                }
                //get value from head
                if(StringUtils.isEmpty(valueCode)){
                    valueCode = request.getHeader(valueType);
                }
            }
        }

        if(StringUtils.isEmpty(valueCode)){
            valueCode=this.getValueCodeByObject(valueType,point.getArgs());
        }
        return valueCode;
    }
    private String getValueCodeByObject(String valueType,Object[] params) {
        if(params==null){
            return "";
        }
        for (Object obj:params){
            if(obj==null){
                continue;
            }
            Class objClazz = (Class) obj.getClass();
            try {
                Method method=null;
                if(Constants.SGSTOKEN.equals(valueType)){
                    method=Arrays.asList(objClazz.getMethods()).stream().filter(e->e.getName().equals("get"+SGSTOKEN_METHOD)).findFirst().orElse(null);
                }else if(Constants.TOKEN.equals(valueType)){
                    method=Arrays.asList(objClazz.getMethods()).stream().filter(e->e.getName().equals("get"+TOKEN_METHOD)).findFirst().orElse(null);
                }
                if(method!=null){
                    Object objFieldValue=method.invoke(obj);
                    return objFieldValue==null?"":objFieldValue.toString();
                }

            }catch (Exception e) {
                logger.error(e.getMessage(),e);
            }
        }
        return "";
    }
}
