#spring配置
spring:
  redis:
    ##redis 集群环境配置
    cluster:
      nodes: 10.168.128.239:7000,10.168.128.239:7001,10.168.128.239:7002,10.168.128.237:7003,10.168.128.237:7004,10.168.128.237:7005
      commandTimeout: 5000

  kafka:
    bootstrap-servers: 10.168.129.110:9092,**************:9092
    consumer:
      group-id: customerUat
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer

dubbo:
  group:
    gpn: GPN
    gpo: GPO
    gpp: GPP
    commonservice: prod
  port: 29531

zookeeper:
  address: **************:2181,**************:2181,**************:2181

url:
  frameWorkApi: http://cnapp.sgs.net/FrameWorkApi

# GPO 配置
gpo:
  rootPath: /mnt/datadisk1/gpodata
  preorder:
    url: http://cnapp.sgs.net/gpo-api
  biz:
    locationMap: {"LOC8":"11","LOC18":"22","LOC24":"33","LOC10":"44","LOC1":"55","LOC9":"66","LOC19":"77","LOC22":"88","LOC2":"99","LOC3":"10"}
    dmServiceType: 3
    newDMCustomerAccounts: 519258,3496164
    isNewCopyOrder: true
  api:
    url:
      userManagementUrl: https://cnapp.sgs.net/UserManagementApi
      frameWorkApiUrl: https://cnapp.sgs.net/FrameWorkApi
      tracking2ApiUrl: https://cnapp.sgs.net/tracking2api
      # emWebServiceUrl: http://*************/EMWebService_UAT/EMWebService.asmx?WSDL
      customerApiUrl: https://cnapp.sgs.net/CustomerApi
      dffApiUrl: http://cnapp.sgs.net/DFFV2Api
      sgsmartApi: https://www.sgsmart-online.com/sgsmartApi/
      # newSgsMartApi: https://www.sgsmart-online.com/api/
      newSgsMartApi: https://cnsgsmart.sgs.net/api/
      otsnotes2Api: https://cnapp.sgs.net/gpn-api
      preOrder2ApiUrl: https://cnapp.sgs.net/tracking2api
      cigeneralApi: https://cnapp.sgs.net/cigeneralApi
      # olbWebServiceUrl: https://olb-uat.cn.sgs.com/olb_uat/services/OLBtaobaoService.asmx?wsdl
      frameWorkApiServiceType: /basicData/api/v1/get/serviceType
      frameWorkApiOrderNo: /numberRule/api/v1/get/orderNo
      queryUserBuLocationLabInfo: /dimension/queryUserBuLationLabInfo
      trimsApiUrl: https://trimschina-azure.sgs.net/trimsApi
      otsnotesApi: https://cnapp.sgs.net/OTSNotesApi
      # dmWebserviceAddress: https://ilayer-uat.sgs.net:443/soa-infra/services/sgs-reports/ReportReceiver_Ots/client?WSDL
      # dmDelwsAddress: https://ilayer-uat.sgs.net/soa-infra/services/sgs-reports/ReportRouter/reportdeleterouter?wsdl
      notificationApiUrl: https://cnapp.sgs.net/NotificationApi
      localIlayerOpenapiUrl: http://cnlocalilayer.sgs.net/openapi
      paymentBackOfficeUrl: https://cnapp.sgs.net/backoffice
      baseUrl: http://cnapp.sgs.net

datasource:
  dynamic:
    primary: gpo
    instance:
      gpo:
        primary: write
        productLines:
          - MR
          - HL
          - AUTO
          - EE
          - IND-PL
          - RSTS
        datasource:
          write:
            driver-class-name: com.mysql.jdbc.Driver
            url: ********************************************************************************************************************************************************************************************************************************************************************************
            username: gpo_user@sgsdbcne2mysqlgpopreordermasterprod
            password: xxxxxxxxxxxxxxxxxxxxx
          read:
            driver-class-name: com.mysql.jdbc.Driver
            url: **********************************************************************************************************************************************************************************************************************************************************************************************************
            username: gpo_user@sgsdbcne2mysqlgpopreordermasterrepprod
            password: xxxxxxxxxxxxxxxxxxxxx

      sl:
        primary: write
        productLines:
          - SL
        datasource:
          write:
            driver-class-name: com.mysql.jdbc.Driver
            url: ******************************************************************************************************************************************************************************************************************************
            username: preorder_read@sgsdbcne2mysqlslpreordermasterProd
            password: dJaGz5FEH^av7%riFww2
          read:
            driver-class-name: com.mysql.jdbc.Driver
            url: *********************************************************************************************************************************************************************************************************************************
            username: preorder_read@sgsdbcne2mysqlslpreordermasterrepProd
            password: dJaGz5FEH^av7%riFww2
      common:
        primary: write
        datasource:
          write:
            driver-class-name: com.mysql.jdbc.Driver
            url: ****************************************************************************************************************************************************************************************
            username: precommon_user@sgsdbcne2mysqlgpopreordermasterprod
            password: xxxxxxxxxxxxxxxxxxxxx
          read:
            driver-class-name: com.mysql.jdbc.Driver
            url: *******************************************************************************************************************************************************************************************
            username: precommon_user@sgsdbcne2mysqlgpopreordermasterrepprod
            password: xxxxxxxxxxxxxxxxxxxxx

customer:
  group: customerProd

# \uFFFD\uFFFD\u02B6\uFFFD\u01F7\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u03E2\uFFFD\uFFFDsgsmart
enable:
  sendMsgToSgsmart: true
  toDmOld: false

isDisableOldDFF: true
