#spring配置
spring:
  redis:
    ##redis 集群环境配置
    cluster:
      nodes: 10.168.128.239:7000,10.168.128.239:7001,10.168.128.239:7002,10.168.128.237:7003,10.168.128.237:7004,10.168.128.237:7005
      commandTimeout: 5000

  kafka:
    bootstrap-servers: 10.168.129.110:9092,**************:9092
    consumer:
      group-id: customerDev
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer

dubbo:
  group:
    gpn: GPN
    gpo: GPO
    gpp: GPP
    commonservice: dev
  port: 29531

zookeeper:
  address: **************:2181,**************:2181,**************:2181

url:
  frameWorkApi: http://cnapp.sgs.net/FrameWorkApi

# GPO 配置
gpo:
  rootPath: /mnt/datadisk1/gpodata
  preorder:
    url: http://cnapp.sgs.net/gpo-api
  biz:
    locationMap: {"LOC8":"11","LOC18":"22","LOC24":"33","LOC10":"44","LOC1":"55","LOC9":"66","LOC19":"77","LOC22":"88","LOC2":"99","LOC3":"10"}
    dmServiceType: 3
    newDMCustomerAccounts: 519258,3496164
    isNewCopyOrder: true
  api:
    url:
      userManagementUrl: https://cnapp.sgs.net/UserManagementApi
      frameWorkApiUrl: https://cnapp.sgs.net/FrameWorkApi
      tracking2ApiUrl: https://cnapp.sgs.net/tracking2api
      customerApiUrl: https://cnapp.sgs.net/CustomerApi
      dffApiUrl: http://cnapp.sgs.net/DFFV2Api
      sgsmartApi: https://www.sgsmart-online.com/sgsmartApi/
      newSgsMartApi: https://cnsgsmart.sgs.net/api/
      otsnotes2Api: https://cnapp.sgs.net/gpn-api
      preOrder2ApiUrl: https://cnapp.sgs.net/tracking2api
      cigeneralApi: https://cnapp.sgs.net/cigeneralApi
      frameWorkApiServiceType: /basicData/api/v1/get/serviceType
      frameWorkApiOrderNo: /numberRule/api/v1/get/orderNo
      queryUserBuLocationLabInfo: /dimension/queryUserBuLationLabInfo
      trimsApiUrl: https://trimschina-azure.sgs.net/trimsApi
      otsnotesApi: https://cnapp.sgs.net/OTSNotesApi
      notificationApiUrl: https://cnapp.sgs.net/NotificationApi
      localIlayerOpenapiUrl: http://cnlocalilayer.sgs.net/openapi
      paymentBackOfficeUrl: https://cnapp.sgs.net/backoffice
      baseUrl: http://cnapp.sgs.net

datasource:
  dynamic:
    primary: gpo
    instance:
      gpo:
        primary: write
        productLines:
          - MR
          - HL
          - AUTO
          - EE
          - IND-PL
          - RSTS
        datasource:
          write:
            driver-class-name: com.mysql.jdbc.Driver
            url: *******************************************************************************************************************************************************************************************************************************************************************************
            username: gpo_user@sgsdbcne2mysqlgpopreordermasterdev
            password: xxxxxxxxxxxxxxxxxxxxx
          read:
            driver-class-name: com.mysql.jdbc.Driver
            url: *********************************************************************************************************************************************************************************************************************************************************************************************************
            username: gpo_user@sgsdbcne2mysqlgpopreordermasterrepdev
            password: xxxxxxxxxxxxxxxxxxxxx

      sl:
        primary: write
        productLines:
          - SL
        datasource:
          write:
            driver-class-name: com.mysql.jdbc.Driver
            url: *****************************************************************************************************************************************************************************************************************************
            username: preorder_read@sgsdbcne2mysqlslpreordermasterdev
            password: dJaGz5FEH^av7%riFww2
          read:
            driver-class-name: com.mysql.jdbc.Driver
            url: ********************************************************************************************************************************************************************************************************************************
            username: preorder_read@sgsdbcne2mysqlslpreordermasterrepdev
            password: dJaGz5FEH^av7%riFww2
      common:
        primary: write
        datasource:
          write:
            driver-class-name: com.mysql.jdbc.Driver
            url: ***************************************************************************************************************************************************************************************
            username: precommon_user@sgsdbcne2mysqlgpopreordermasterdev
            password: xxxxxxxxxxxxxxxxxxxxx
          read:
            driver-class-name: com.mysql.jdbc.Driver
            url: ******************************************************************************************************************************************************************************************
            username: precommon_user@sgsdbcne2mysqlgpopreordermasterrepdev
            password: xxxxxxxxxxxxxxxxxxxxx

customer:
  group: customerDev

# 启用发送消息到sgsmart
enable:
  sendMsgToSgsmart: true
  toDmOld: false

isDisableOldDFF: true

# SnailJob配置 - 开发环境
snail-job:
  server:
    host: *************
    port: 17888
  # 名称空间ID
  namespace: soda
  # 令牌
  token: SJ_GvA7wSSMIeeJpARlWFGpSs3NLz5QK1lI
  # 组配置 - 开发环境
  group: soda-preorder-dev
  # 可选但重要的配置
  enabled: true                       # 是否启用
  register-interval: 30               # 注册间隔（秒）
  heartbeat-interval: 10              # 心跳间隔（秒）
  # AOP执行顺序配置 - 确保SnailJob拦截器优先执行
  aop:
    order: -1000                      # 设置较小的值确保优先级高于PreOrderAspect
  # 客户端配置
  client:
    connect-timeout: 5000             # 连接超时（毫秒）
    read-timeout: 10000               # 读取超时（毫秒）
  # 重试配置
  retry:
    max-attempts: 3                   # 最大重试次数
    initial-interval: 1000            # 初始重试间隔（毫秒）
  # 日志配置
  log:
    level: DEBUG                      # 日志级别

logging:
  level:
    org.springframework.boot.autoconfigure: DEBUG
    com.aizuda.snailjob: DEBUG

# 启用自动配置报告
debug: true
