package com.sgs.preorder.facade.model.ordercopy;

import java.util.List;
import java.util.Map;

public class ConclusionSummary {
    private String reportNo;
    private String orderNo;
    private Integer type;
    private String sgsToken;
    private String buID;
    private String locationID;
    private String newOrderNo;
    private List<String> matrixListIds;
    //拆分循环的顺序，用来设置reportNO后面的序号
    private Integer reportNoSeq;


    private Map<String, String> oldSampleIds;

    private List<Map<String,String>>matrixList;
    private List<Map<String,String>>sampleList;

    private Map<String, List<String>> testLineSampleList;

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getSgsToken() {
        return sgsToken;
    }

    public void setSgsToken(String sgsToken) {
        this.sgsToken = sgsToken;
    }

    public String getBuID() {
        return buID;
    }

    public void setBuID(String buID) {
        this.buID = buID;
    }

    public String getLocationID() {
        return locationID;
    }

    public void setLocationID(String locationID) {
        this.locationID = locationID;
    }

    public String getNewOrderNo() {
        return newOrderNo;
    }

    public void setNewOrderNo(String newOrderNo) {
        this.newOrderNo = newOrderNo;
    }

    public List<String> getMatrixListIds() {
        return matrixListIds;
    }

    public void setMatrixListIds(List<String> matrixListIds) {
        this.matrixListIds = matrixListIds;
    }

    public Integer getReportNoSeq() {
        return reportNoSeq;
    }

    public void setReportNoSeq(Integer reportNoSeq) {
        this.reportNoSeq = reportNoSeq;
    }

    public Map<String, String> getOldSampleIds() {
        return oldSampleIds;
    }

    public void setOldSampleIds(Map<String, String> oldSampleIds) {
        this.oldSampleIds = oldSampleIds;
    }

    public List<Map<String, String>> getMatrixList() {
        return matrixList;
    }

    public void setMatrixList(List<Map<String, String>> matrixList) {
        this.matrixList = matrixList;
    }

    public List<Map<String, String>> getSampleList() {
        return sampleList;
    }

    public void setSampleList(List<Map<String, String>> sampleList) {
        this.sampleList = sampleList;
    }

    public Map<String, List<String>> getTestLineSampleList() {
        return testLineSampleList;
    }

    public void setTestLineSampleList(Map<String, List<String>> testLineSampleList) {
        this.testLineSampleList = testLineSampleList;
    }
}
