package com.sgs.preorder.facade.model.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 */

public enum PreOrderEventEnum {

    CREATED(1, "订单已创建"),

    CONFIRMED(3, "订单已确认"),

    TESTING(8, "订单测试中"),

    REPORTING(9, "订单报告中"),

    COMPLETED(10, "订单已完成"),

    CLOSED(5, "订单已关闭"),

    PENDING(6, "订单挂起"),

    CANCELLED(7, "订单已取消"),
    ;

    private int eventType;

    private String description;


    PreOrderEventEnum(int eventType, String description) {
        this.eventType = eventType;
        this.description = description;
    }

    public int getEventType() {
        return eventType;
    }

    public void setEventType(int eventType) {
        this.eventType = eventType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public static boolean check(int eventType, PreOrderEventEnum... eventTypes) {
        if (Objects.isNull(eventTypes) || eventTypes.length == 0) {
            return false;
        }
        for (PreOrderEventEnum type : eventTypes) {
            if (Objects.isNull(type)) {
                return false;
            }
            if (type.eventType == eventType) {
                return true;
            }
        }
        return false;
    }

    public static PreOrderEventEnum find(int eventType) {
        for (PreOrderEventEnum e : PreOrderEventEnum.values()) {
            if (e.getEventType() == eventType) {
                return e;
            }
        }
        return null;
    }
}
